﻿using RoboMentors.EventManagement.EventAwards;
using RoboMentors.EventManagement.RobotEvents;
using Volo.Abp.Application.Dtos;

namespace RoboMentors.Judging.Awards;

public class AwardDto : AuditedEntityDto<int>
{
  public string Keyword { get; private set; } = string.Empty;

  public string Abbrev { get; set; } = string.Empty;

  public string Title { get; set; } = string.Empty;

  public EventProgram Program { get; set; }

  public AwardType Type { get; set; }

  public string Description { get; set; } = string.Empty;

  public string Script { get; set; } = string.Empty;

  public string Icon { get; set; } = string.Empty;

  public bool IncludeOnInitialRanking { get; set; }

  public bool IncludeOnFollowUpRanking { get; set; }

  public int FollowUpInterviewDefaultMinutes { get; set; }

  public int DefaultOrder { get; set; }

  public int VIQOrder { get; set; }

}
