trigger:
  branches:
    include:
    - main
  paths:
    exclude:
    - pipelines/*

pool:
  vmImage: 'ubuntu-latest'  # Changed to Linux for better Docker support

variables:
  assemblyVersion: '1.0.0.$(Build.BuildId)'
  BuildConfiguration: 'Release'
  containerRegistry: 'your-acr-name.azurecr.io'  # Replace with your ACR name
  imageRepository: 'robomentos'
  dockerfilePath: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.Blazor/Dockerfile'
  tag: '$(Build.BuildId)'

  # Azure Container Instance variables
  resourceGroupName: 'rg-robomentos-prod'  # Replace with your resource group
  containerInstanceName: 'aci-robomentos-web'
  dnsNameLabel: 'robomentos-web'  # This will create robomentos-web.region.azurecontainer.io

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build Application'
    steps:
    - task: UseDotNet@2
      inputs:
        version: '9.0.x'
      displayName: 'Install .NET 9.0 SDK'

    - task: DotNetCoreCLI@2
      displayName: 'Install ABP CLI'
      inputs:
        command: 'custom'
        custom: 'tool'
        arguments: 'install -g Volo.Abp.Cli'

    - task: DotNetCoreCLI@2
      displayName: 'Install ABP client-side packages'
      inputs:
        command: 'custom'
        custom: 'abp'
        arguments: 'install-libs'
        workingDirectory: '$(Build.SourcesDirectory)/RoboMentors'

    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet packages'
      inputs:
        command: 'restore'
        projects: 'RoboMentors/**/*.csproj'
        verbosityRestore: 'Normal'

    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: 'RoboMentors/**/*.csproj'
        arguments: '--configuration $(BuildConfiguration) --no-restore'

    - task: DotNetCoreCLI@2
      displayName: 'Run tests'
      inputs:
        command: 'test'
        projects: 'RoboMentors/test/**/*.csproj'
        arguments: '--configuration $(BuildConfiguration) --no-build --collect:"XPlat Code Coverage"'

    - task: DotNetCoreCLI@2
      displayName: 'Publish Blazor App'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'RoboMentors/src/RoboMentors.Blazor/RoboMentors.Blazor.csproj'
        arguments: '--configuration $(BuildConfiguration) --output $(Build.ArtifactStagingDirectory)/blazor --no-build'

    - task: DotNetCoreCLI@2
      displayName: 'Publish DB Migrator'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'RoboMentors/src/RoboMentors.DbMigrator/RoboMentors.DbMigrator.csproj'
        arguments: '--configuration $(BuildConfiguration) --output $(Build.ArtifactStagingDirectory)/dbmigrator --no-build'

    - task: PublishBuildArtifacts@1
      displayName: 'Publish Build Artifacts'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'

- stage: Docker
  displayName: 'Build and Push Docker Images'
  dependsOn: Build
  condition: succeeded()
  jobs:
  - job: BuildDockerImages
    displayName: 'Build Docker Images'
    steps:
    - task: DownloadBuildArtifacts@0
      displayName: 'Download Build Artifacts'
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'drop'
        downloadPath: '$(System.ArtifactsDirectory)'

    # Copy published files to Docker context
    - task: CopyFiles@2
      displayName: 'Copy Blazor App to Docker Context'
      inputs:
        SourceFolder: '$(System.ArtifactsDirectory)/drop/blazor'
        Contents: '**'
        TargetFolder: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.Blazor/bin/Release/net9.0/publish'

    # Generate OpenIddict certificate for production
    - task: DotNetCoreCLI@2
      displayName: 'Generate OpenIddict Certificate'
      inputs:
        command: 'custom'
        custom: 'dev-certs'
        arguments: 'https -v -ep $(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.Blazor/openiddict.pfx -p c6177a4a-55d9-4034-b9d5-1ed0ffb39c0d'

    # Build and push Blazor app Docker image
    - task: Docker@2
      displayName: 'Build and Push Blazor App Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageRepository)-web'
        command: 'buildAndPush'
        Dockerfile: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.Blazor/Dockerfile'
        buildContext: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.Blazor'
        tags: |
          $(tag)
          latest

    # Copy DB Migrator files to Docker context
    - task: CopyFiles@2
      displayName: 'Copy DB Migrator to Docker Context'
      inputs:
        SourceFolder: '$(System.ArtifactsDirectory)/drop/dbmigrator'
        Contents: '**'
        TargetFolder: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.DbMigrator/bin/Release/net9.0/publish'

    # Build and push DB Migrator Docker image
    - task: Docker@2
      displayName: 'Build and Push DB Migrator Image'
      inputs:
        containerRegistry: '$(containerRegistry)'
        repository: '$(imageRepository)-dbmigrator'
        command: 'buildAndPush'
        Dockerfile: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.DbMigrator/Dockerfile'
        buildContext: '$(Build.SourcesDirectory)/RoboMentors/src/RoboMentors.DbMigrator'
        tags: |
          $(tag)
          latest

- stage: Deploy
  displayName: 'Deploy to Azure Container Instances'
  dependsOn: Docker
  condition: succeeded()
  jobs:
  - deployment: DeployToACI
    displayName: 'Deploy to Azure Container Instances'
    environment: 'production'
    strategy:
      runOnce:
        deploy:
          steps:
          # Run DB Migration first
          - task: AzureContainerInstances@0
            displayName: 'Run Database Migration'
            inputs:
              azureSubscription: 'your-service-connection'  # Replace with your service connection
              resourceGroupName: '$(resourceGroupName)'
              location: 'East US'  # Replace with your preferred region
              containerName: '$(containerInstanceName)-dbmigrator'
              image: '$(containerRegistry)/$(imageRepository)-dbmigrator:$(tag)'
              restartPolicy: 'Never'
              environmentVariables: |
                ConnectionStrings__Default=your-production-connection-string
              cpu: 1
              memory: 1.5

          # Deploy main web application
          - task: AzureContainerInstances@0
            displayName: 'Deploy Web Application'
            inputs:
              azureSubscription: 'your-service-connection'  # Replace with your service connection
              resourceGroupName: '$(resourceGroupName)'
              location: 'East US'  # Replace with your preferred region
              containerName: '$(containerInstanceName)'
              image: '$(containerRegistry)/$(imageRepository)-web:$(tag)'
              dnsNameLabel: '$(dnsNameLabel)'
              ports: '80 443'
              restartPolicy: 'Always'
              environmentVariables: |
                ASPNETCORE_ENVIRONMENT=Production
                ConnectionStrings__Default=your-production-connection-string
                App__SelfUrl=https://$(dnsNameLabel).eastus.azurecontainer.io
                App__RedirectAllowedUrls=https://$(dnsNameLabel).eastus.azurecontainer.io
                AuthServer__Authority=https://$(dnsNameLabel).eastus.azurecontainer.io
                AuthServer__RequireHttpsMetadata=true
                AuthServer__CertificatePassPhrase=c6177a4a-55d9-4034-b9d5-1ed0ffb39c0d
                StringEncryption__DefaultPassPhrase=your-encryption-passphrase
              cpu: 2
              memory: 4

