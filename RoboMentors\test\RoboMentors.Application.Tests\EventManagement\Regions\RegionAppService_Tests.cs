﻿using Shouldly;
using System;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Xunit;

namespace RoboMentors.EventManagement.Regions;

public abstract class RegionAppService_Tests<TStartupModule> : RoboMentorsApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
  private readonly IRegionAppService _regionAppService;

  protected RegionAppService_Tests()
  {
    _regionAppService = GetRequiredService<IRegionAppService>();
  }

	[Fact]
	public async Task Should_Sync_And_Get_Regions()
	{
		// Act
		var start = DateTime.Now.AddMonths(-3);
		var results1 = await _regionAppService.SyncRegionsAsync(start);
		var results2 = await _regionAppService.GetRegionsAsync();

		//Assert
		results1.Success.ShouldBeTrue();
		results2.Count.ShouldBeGreaterThan(0);
	}

}
