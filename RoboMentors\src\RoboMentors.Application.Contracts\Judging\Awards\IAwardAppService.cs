﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;

namespace RoboMentors.Judging.Awards;

public interface IAwardAppService : IApplicationService
{
  // AwardAppService Methods 
  Task<AwardDto> GetDtoAsync(int id);
  Task<List<AwardDto>> GetDtosAsync();
  Task<PagedResultDto<AwardDto>> GetPagedListAsync(GetListDto input);
  Task<List<AwardKeywordLookupDto>> GetKeywordLookupAsync();
  Task<ApiResult<AwardDto>> CreateAsync(CreateUpdateAwardDto input);
  Task<ApiResult<AwardDto>> UpdateAsync(CreateUpdateAwardDto input);
  Task DeleteAsync(int id);
}
