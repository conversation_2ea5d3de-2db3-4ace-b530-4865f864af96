﻿using RoboMentors.Judging.Interviews;
using System;
using Volo.Abp.Application.Dtos;

namespace RoboMentors.Judging.Questions;

public class QuestionDto : AuditedEntityDto<Guid>
{
  public InterviewType InterviewType { get; set; }

  public int AwardId { get; set; }

  public int Order { get; set; }

  public string NoteKeyword { get; set; } = string.Empty;

  public string Text { get; set; } = string.Empty;

  // Display Variables
  public string InterviewTypeText { get; set; } = string.Empty;
  public int AwardOrder { get; set; }
  public string AwardKeyword { get; set; } = string.Empty;

}
