﻿using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using RoboMentors.Judging.Awards;
using RoboMentors.Identity.Permissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;

namespace RoboMentors.Blazor.Components.Pages.Judging.Awards;

public partial class Awards : RoboMentorsComponentBase
{
  // Service Injections
  [Inject]
  public IAwardAppService AwardAppService { get; set; }

  // Page Variables
  protected PageToolbar Toolbar { get; } = new();

  private List<AwardDto> AwardDtos = new();
  private CreateUpdateAwardDto CUAwardDto = new();
  private int nextId = 0;

  private int PageSize = RoboMentorsSharedConsts.PageSizeDefault;

  protected bool CanCreate = false;
  protected bool CanEdit = false;
  protected bool CanDelete = false;
  protected bool IsReadOnly = true;

  private Modal CreateUpdateModal = new();
  private FormMode FormMode = FormMode.Unknown;

  private Validations ValidationsRef = new();

  private List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new();

  protected override async Task OnInitializedAsync()
  {
    PageSetup();
    await SetPermissionsAsync().ConfigureAwait(false);
    await GetDtosAsync().ConfigureAwait(false);
  }

  protected void PageSetup()
  {
    Toolbar.AddButton(L["Award:New"],
        () => { OpenModalForNew(); return Task.CompletedTask; },
        icon: IconName.Add,
        color: Color.Primary,
        requiredPolicyName: RoboMentorsPermissions.Awards.Create
    );

    BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Maintenance"]));
    BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Awards"]));
  }

  private async Task SetPermissionsAsync()
  {
    CanCreate = await AuthorizationService.IsGrantedAsync(RoboMentorsPermissions.Awards.Create);
    CanEdit = await AuthorizationService.IsGrantedAsync(RoboMentorsPermissions.Awards.Edit);
    CanDelete = await AuthorizationService.IsGrantedAsync(RoboMentorsPermissions.Awards.Delete);
  }

  private async Task GetDtosAsync()
  {
    AwardDtos = await AwardAppService.GetDtosAsync().ConfigureAwait(false);
  }

  private void OpenModalForNew()
  {
    ValidationsRef.ClearAll();
    nextId = AwardDtos.Select(x => x.Id).Max() + 1;
    CUAwardDto = new()
    {
      Id = nextId,
      DefaultOrder = nextId,
      VIQOrder = nextId
    };
    FormMode = FormMode.New;
    IsReadOnly = CanCreate == false;
    CreateUpdateModal.Show();
  }

  private void OpenModalForEdit(AwardDto award)
  {
    ValidationsRef.ClearAll();
    CUAwardDto = ObjectMapper.Map<AwardDto, CreateUpdateAwardDto>(award);
    FormMode = FormMode.Edit;
    IsReadOnly = CanEdit == false;
    CreateUpdateModal.Show();
  }

  private async Task CloseModal()
  {
    await CreateUpdateModal.Hide();
  }

  private async Task SaveAsync()
  {
    if (await ValidationsRef.ValidateAll() == false)
    {
      return;
    }
    var result = new ApiResult<AwardDto>();
    if (FormMode == FormMode.New)
    {
      result = await AwardAppService.CreateAsync(CUAwardDto).ConfigureAwait(false);
    }
    else
    {
      result = await AwardAppService.UpdateAsync(CUAwardDto).ConfigureAwait(false);
    }
    if (result.Success)
    {
      await CreateUpdateModal.Hide();
      await GetDtosAsync().ConfigureAwait(false);
    }
    else
    {
      var message = L[result.ErrorMessage, result.ErrorMessageParam];
      await Message.Warn(message, "Errors:Awards");
    }
  }

  private async Task DeleteAsync()
  {
    if (await Message.Confirm(L["AreYouSureToDelete", $"{CUAwardDto.Keyword} Award"], L["AreYouSure"]) == false)
    {
      return;
    }
    await AwardAppService.DeleteAsync(CUAwardDto.Id).ConfigureAwait(false);
    await GetDtosAsync().ConfigureAwait(false);
    await CreateUpdateModal.Hide();
  }

  private void ValidateUniqueKeyword(ValidatorEventArgs e)
  {
    var keyword = Convert.ToString(e.Value);
    if (keyword.IsNullOrWhiteSpace() == true)
    {
      e.Status = ValidationStatus.Error;
      e.ErrorText = L["Errors:Awards:Required"];
      return;
    }
    var award = AwardDtos.Where(x => x.Keyword.ToLower() == keyword.ToLower()).FirstOrDefault();
    if (award != null && award.Id != CUAwardDto?.Id)
    {
      e.Status = ValidationStatus.Error;
      e.ErrorText = @L["Errors:Awards:KeywordAlreadyExists", keyword];
      return;
    }
    e.Status = ValidationStatus.Success;
    return;
  }
}
