﻿namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public partial class PageMeta
{
  [Newtonsoft.Json.JsonProperty("current_page", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int Current_page { get; set; }

  [Newtonsoft.Json.JsonProperty("first_page_url", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string First_page_url { get; set; }

  [Newtonsoft.Json.JsonProperty("from", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int From { get; set; }

  [Newtonsoft.Json.JsonProperty("last_page", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int Last_page { get; set; }

  [Newtonsoft.Json.JsonProperty("last_page_url", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Last_page_url { get; set; }

  [Newtonsoft.Json.JsonProperty("next_page_url", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Next_page_url { get; set; }

  [Newtonsoft.Json.JsonProperty("path", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Path { get; set; }

  [Newtonsoft.Json.JsonProperty("per_page", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int Per_page { get; set; }

  [Newtonsoft.Json.JsonProperty("prev_page_url", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Prev_page_url { get; set; }

  [Newtonsoft.Json.JsonProperty("to", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int To { get; set; }

  [Newtonsoft.Json.JsonProperty("total", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int Total { get; set; }

  private System.Collections.Generic.IDictionary<string, object> _additionalProperties = new System.Collections.Generic.Dictionary<string, object>();

  [Newtonsoft.Json.JsonExtensionData]
  public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
  {
    get { return _additionalProperties; }
    set { _additionalProperties = value; }
  }
}
