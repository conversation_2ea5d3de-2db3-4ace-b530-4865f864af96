﻿using RoboMentors.EventManagement.RobotEvents;

namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public partial class RETeam
{
  [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
  public int Id { get; set; }

  [Newtonsoft.Json.JsonProperty("number", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
  public string Number { get; set; }

  [Newtonsoft.Json.JsonProperty("team_name", NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Team_name { get; set; }

  [Newtonsoft.Json.JsonProperty("robot_name", NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Robot_name { get; set; }

  [Newtonsoft.Json.JsonProperty("organization", NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Organization { get; set; }

  [Newtonsoft.Json.JsonProperty("location", NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public RELocation Location { get; set; }

  [Newtonsoft.Json.JsonProperty("registered", NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public bool Registered { get; set; }

  [Newtonsoft.Json.JsonProperty("program", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required]
  public IdInfo Program { get; set; } = new IdInfo();

  [Newtonsoft.Json.JsonProperty("grade", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
  public Grade Grade { get; set; }

  private System.Collections.Generic.IDictionary<string, object> _additionalProperties = new System.Collections.Generic.Dictionary<string, object>();

  [Newtonsoft.Json.JsonExtensionData]
  public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
  {
    get { return _additionalProperties; }
    set { _additionalProperties = value; }
  }
}
