﻿using Microsoft.EntityFrameworkCore;
using RoboMentors.EntityFrameworkCore;
using RoboMentors.Judging.Interviews;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace RoboMentors.Judging.Questions;

public class EfCoreQuestionRepository :
    EfCoreRepository<RoboMentorsDbContext, Question>,
    IQuestionRepository
{
  public EfCoreQuestionRepository(
      IDbContextProvider<RoboMentorsDbContext> dbContextProvider)
      : base(dbContextProvider)
  {
  }

  public async Task<Question?> FindAsync(Guid id)
  {
    var dbSet = await GetDbSetAsync();
    return await dbSet
        .Where(x => x.Id == id)
        .FirstOrDefaultAsync();
  }

  public async Task<List<Question>> GetListAsync(InterviewType? interviewType = null, int? awardId = null)
  {
    var dbSet = await GetDbSetAsync();
    return await dbSet
      .WhereIf(interviewType != null, x => x.InterviewType == interviewType)
      .WhereIf(awardId != null && awardId != 99, x => x.AwardId == awardId)
      .OrderBy(x => x.InterviewType)
      .ThenBy(x => x.AwardId)
      .ThenBy(x => x.Order)
      .ToListAsync();
  }
}
