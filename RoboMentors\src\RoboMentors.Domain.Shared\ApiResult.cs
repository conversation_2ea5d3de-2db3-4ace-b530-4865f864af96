﻿using System.Collections.Generic;

namespace RoboMentors;

public class ApiResult<T>
{
  public ApiResult()
  {
    Success = false;
    ErrorMessage = string.Empty;
    ErrorMessageParam = string.Empty;
    Log = new();
    Data = default(T);
  }
  public bool Success { get; set; }
  public string ErrorMessage { get; set; }
  public string ErrorMessageParam { get; set; }
  public List<string> Log { get; set; }
  public T? Data { get; set; }
}
