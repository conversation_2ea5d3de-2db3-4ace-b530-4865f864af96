﻿using RoboMentors.EventManagement.RobotEvents;

namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public partial class REEvent
{
  [Newtonsoft.Json.JsonProperty("id", Required = Newtonsoft.Json.Required.Always)]
  public int Id { get; set; }

  [Newtonsoft.Json.JsonProperty("sku", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
  public string Sku { get; set; }

  [Newtonsoft.Json.JsonProperty("name", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
  public string Name { get; set; }

  [Newtonsoft.Json.JsonProperty("start", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public System.DateTimeOffset Start { get; set; }

  [Newtonsoft.Json.JsonProperty("end", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public System.DateTimeOffset End { get; set; }

  [Newtonsoft.Json.JsonProperty("season", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required]
  public IdInfo Season { get; set; } = new IdInfo();

  [Newtonsoft.Json.JsonProperty("program", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required]
  public IdInfo REProgram { get; set; } = new IdInfo();

  [Newtonsoft.Json.JsonProperty("location", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required]
  public RELocation Location { get; set; } = new RELocation();

  [Newtonsoft.Json.JsonProperty("divisions", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public System.Collections.Generic.ICollection<REDivision> REDivisions { get; set; }

  [Newtonsoft.Json.JsonProperty("level", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
  public EventLevel Level { get; set; }

  [Newtonsoft.Json.JsonProperty("ongoing", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public bool Ongoing { get; set; }

  [Newtonsoft.Json.JsonProperty("awards_finalized", Required = Newtonsoft.Json.Required.DisallowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public bool Awards_finalized { get; set; }

  [Newtonsoft.Json.JsonProperty("event_type", Required = Newtonsoft.Json.Required.AllowNull, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
  public EventType Event_type { get; set; }

  private System.Collections.Generic.IDictionary<string, object> _additionalProperties = new System.Collections.Generic.Dictionary<string, object>();

  [Newtonsoft.Json.JsonExtensionData]
  public System.Collections.Generic.IDictionary<string, object> AdditionalProperties
  {
    get { return _additionalProperties; }
    set { _additionalProperties = value; }
  }
}
