﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RoboMentors.Migrations
{
    /// <inheritdoc />
    public partial class Create_Awards_Questions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "JdgAwards",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false),
                    Keyword = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    Abbrev = table.Column<string>(type: "nvarchar(3)", maxLength: 3, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Program = table.Column<int>(type: "int", nullable: false),
                    Type = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Script = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Icon = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    IncludeOnInitialRanking = table.Column<bool>(type: "bit", nullable: false),
                    IncludeOnFollowUpRanking = table.Column<bool>(type: "bit", nullable: false),
                    FollowUpInterviewDefaultMinutes = table.Column<int>(type: "int", nullable: false),
                    DefaultOrder = table.Column<int>(type: "int", nullable: false),
                    VIQOrder = table.Column<int>(type: "int", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JdgAwards", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "JdgQuestions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InterviewType = table.Column<int>(type: "int", nullable: false),
                    AwardId = table.Column<int>(type: "int", nullable: false),
                    Order = table.Column<int>(type: "int", nullable: false),
                    NoteKeyword = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Text = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    CreationTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatorId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    LastModificationTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifierId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_JdgQuestions", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_JdgAwards_Keyword",
                table: "JdgAwards",
                column: "Keyword");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "JdgAwards");

            migrationBuilder.DropTable(
                name: "JdgQuestions");
        }
    }
}
