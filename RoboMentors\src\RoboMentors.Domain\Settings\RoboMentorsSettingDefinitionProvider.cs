﻿using Microsoft.Extensions.Configuration;
using System.Reflection;
using Volo.Abp.Settings;

namespace RoboMentors.Settings;

public class RoboMentorsSettingDefinitionProvider : SettingDefinitionProvider
{
  readonly IConfiguration configuration;

  public RoboMentorsSettingDefinitionProvider(IConfiguration configuration)
  {
    this.configuration = configuration;
  }

  public override void Define(ISettingDefinitionContext context)
  {
    //Define your own settings here. Example:
    //context.Add(new SettingDefinition(RoboMentorsV3Settings.MySetting1));

    var deploymentEnvironment = configuration["Deployment_Environment"];
    context.Add(new SettingDefinition(RoboMentorsSettings.DeploymentEnvironment, deploymentEnvironment));

    // Source: https://edi.wang/post/2018/9/27/get-app-version-net-core
    // it also shows how to get the fileVersion and version as well
    var entryAssembly = Assembly.GetEntryAssembly();
    var assemblyVersion = entryAssembly.GetName().Version;
    context.Add(new SettingDefinition(RoboMentorsSettings.Version, assemblyVersion.ToString()));
  }
}
