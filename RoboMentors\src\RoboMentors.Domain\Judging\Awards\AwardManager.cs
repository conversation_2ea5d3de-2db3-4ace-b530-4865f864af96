﻿using RoboMentors.EventManagement.EventAwards;
using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Services;

namespace RoboMentors.Judging.Awards;

public class AwardManager : DomainService
{
  private readonly IAwardRepository _awardRepository;

  public AwardManager(IAwardRepository awardRepository)
  {
    _awardRepository = awardRepository;
  }

  public async Task<ApiResult<Award>> CreateAsync(
      int id,
      [NotNull] string keyword,
      [NotNull] string abbrev,
      [NotNull] string title,
      EventProgram program,
      AwardType type,
      [NotNull] string description,
      [NotNull] string script,
      [NotNull] string icon,
      bool includeOnInitialRanking,
      bool includeOnFollowUpRanking,
      int followUpInterviewDefaultMinutes,
      int defaultOrder,
      int viqOrder)
  {
    var result = new ApiResult<Award>();

    try
    {
      Check.Positive(id, nameof(id));
      Check.NotNullOrEmpty(keyword, nameof(keyword));
      Check.NotNull(abbrev, nameof(abbrev));
      Check.NotNull(title, nameof(title));
      Check.NotNull(description, nameof(description));
      Check.NotNull(script, nameof(script));
      Check.NotNull(icon, nameof(icon));
      Check.Positive(defaultOrder, nameof(defaultOrder));
      Check.Positive(viqOrder, nameof(viqOrder));
    }
    catch (Exception ex)
    {
      result.ErrorMessage = ex.Message;
      return result;
    }

    var existingAward = await _awardRepository.FindAsync(id);
    if (existingAward != null)
    {
      result.ErrorMessage = RoboMentorsDomainErrorCodes.AwardIdAlreadyExists;
      result.ErrorMessageParam = id.ToString();
      return result;
    }

    existingAward = await _awardRepository.FindByKeywordAsync(keyword);
    if (existingAward != null)
    {
      result.ErrorMessage = RoboMentorsDomainErrorCodes.AwardKeywordAlreadyExists;
      result.ErrorMessageParam = keyword.ToString();
      return result;
    }
    result.Success = true;
    result.Data = new Award(
      id, keyword, abbrev, title, 
      program, type,
      description, script, icon,
      includeOnInitialRanking, includeOnFollowUpRanking, followUpInterviewDefaultMinutes,
      defaultOrder, viqOrder
    );
    return result;
  }

  public async Task<ApiResult<Award>> ChangeKeywordAsync(
      [NotNull] Award award,
      [NotNull] string newKeyword)
  {
    var result = new ApiResult<Award>();
    try
    {
      Check.NotNull(award, nameof(award));
      Check.NotNullOrWhiteSpace(newKeyword, nameof(newKeyword));
    }
    catch (Exception ex)
    {
      result.ErrorMessage = ex.Message;
      return result;
    }

    var existingAward = await _awardRepository.FindByKeywordAsync(newKeyword);
    if (existingAward != null && existingAward.Id != award.Id)
    {
      result.ErrorMessage = RoboMentorsDomainErrorCodes.AwardKeywordAlreadyExists;
      result.ErrorMessageParam = newKeyword.ToString();
      return result;
    }
    result.Success = true;
    award.ChangeKeyword(newKeyword);
    result.Data = award;
    return result;
  }

}
