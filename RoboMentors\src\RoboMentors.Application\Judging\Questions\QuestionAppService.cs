﻿using Microsoft.AspNetCore.Authorization;
using RoboMentors.Identity.Permissions;
using RoboMentors.Judging.Awards;
using RoboMentors.Judging.Interviews;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RoboMentors.Judging.Questions;

[Authorize(RoboMentorsPermissions.Questions.Default)]
public class QuestionAppService : RoboMentorsAppService, IQuestionAppService
{
  // Repositories
  private readonly IQuestionRepository _questionRepository;
  private readonly IAwardRepository _awardRepository;

  public QuestionAppService(
    IQuestionRepository questionRepository,
    IAwardRepository awardRepository)
  {
    _questionRepository = questionRepository;
    _awardRepository = awardRepository;
  }

  //*-----
  //* Get Question by Id
  //*-----
  private async Task<T> GetAsync<T>(Guid id)
  {
    var question = await _questionRepository.FindAsync(id).ConfigureAwait(false);
    return ObjectMapper.Map<Question, T>(question);
  }
  public async Task<QuestionDto> GetDtoAsync(Guid id)
  {
    var question = await GetAsync<QuestionDto>(id).ConfigureAwait(false);
    if (question == null) { return new QuestionDto(); }
    return question;
  }

  //*-----
  //* Get Full List of Questions
  //*-----
  private async Task<List<T>> GetListAsync<T>(InterviewType? interviewType = null, int? awardId = null)
  {
    var questions = await _questionRepository.GetListAsync(interviewType: interviewType, awardId: awardId).ConfigureAwait(false);
    return ObjectMapper.Map<List<Question>, List<T>>(questions);
  }
  public async Task<List<QuestionDto>> GetDtosAsync(InterviewType? interviewType = null, int? awardId = null)
  {
    var questionDtos = await GetListAsync<QuestionDto>(interviewType: interviewType, awardId: awardId).ConfigureAwait(false);
    questionDtos = await GetAwardInfoAsync(questionDtos).ConfigureAwait(false);
    return questionDtos;
  }

  private async Task<List<QuestionDto>> GetAwardInfoAsync(List<QuestionDto> questionDtos)
  {
    //  Include Enum & Award Data
    var awards = await _awardRepository.GetListAsync().ConfigureAwait(false);
    foreach (var question in questionDtos)
    {
      question.InterviewTypeText = question.InterviewType.ToString();
      if (question.AwardId == 99)
      {
        question.AwardKeyword = "All";
        question.AwardOrder = 99;
        continue;
      }
      var award = awards.Where(x => x.Id == question.AwardId).FirstOrDefault();
      if (award != null)
      {
        question.AwardKeyword = award.Keyword;
        question.AwardOrder = award.DefaultOrder;
        continue;
      }
      question.AwardKeyword = "Unknown";
      question.AwardOrder = 999;
    }
    questionDtos = questionDtos
        .OrderBy(x => x.InterviewType)
        .ThenBy(x => x.AwardOrder)
        .ThenBy(x => x.Order)
        .ToList();
    return questionDtos;
  }

  //*-----
  //* Create new Question
  //*-----
  [Authorize(RoboMentorsPermissions.Questions.Create)]
  public async Task<ApiResult<QuestionDto>> CreateAsync(CreateUpdateQuestionDto input)
  {
    var result = new ApiResult<Question>();
    var question = new Question(new Guid());
    try
    {
      question = ObjectMapper.Map<CreateUpdateQuestionDto, Question>(input);
      question = await _questionRepository.InsertAsync(question);
      result.Success = true;
      result.Data = question;
    }
    catch (Exception ex)
    {
      result.ErrorMessage = ex.Message;
    }
    return ObjectMapper.Map<ApiResult<Question>, ApiResult<QuestionDto>>(result);
  }

  //*-----
  //* Update Question
  //*-----
  [Authorize(RoboMentorsPermissions.Questions.Edit)]
  public async Task<ApiResult<QuestionDto>> UpdateAsync(CreateUpdateQuestionDto input)
  {
    var question = await _questionRepository.FindAsync(input.Id).ConfigureAwait(false);
    var result = new ApiResult<Question>();
    if (question == null) { return ObjectMapper.Map<ApiResult<Question>, ApiResult<QuestionDto>>(result); }

    if (question.InterviewType != input.InterviewType ||
        question.AwardId != input.AwardId ||
        question.Order != input.Order ||
        (question.NoteKeyword == null && input.NoteKeyword != null) ||
        (question.NoteKeyword != null && input.NoteKeyword == null) ||
        question.NoteKeyword != input.NoteKeyword ||
        question.Text != input.Text)
    {
      question.InterviewType = input.InterviewType;
      question.AwardId = input.AwardId;
      question.Order = input.Order;
      question.NoteKeyword = input.NoteKeyword;
      question.Text = input.Text;
      result.Data = await _questionRepository.UpdateAsync(question);
    }
    result.Success = true;
    return ObjectMapper.Map<ApiResult<Question>, ApiResult<QuestionDto>>(result);
  }

  //*-----
  //* Delete Question
  //*-----
  [Authorize(RoboMentorsPermissions.Questions.Delete)]
  public async Task DeleteAsync(Guid id)
  {
    var question = await _questionRepository.FindAsync(id).ConfigureAwait(false);
    if (question == null) { return; }
    await _questionRepository.DeleteAsync(question);
  }

}
