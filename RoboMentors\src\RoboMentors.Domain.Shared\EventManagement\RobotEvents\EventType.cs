﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RoboMentors.EventManagement.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public enum EventType
{
  [System.Runtime.Serialization.EnumMember(Value = @"tournament")]
  Tournament = 0,

  [System.Runtime.Serialization.EnumMember(Value = @"league")]
  League = 1,

  [System.Runtime.Serialization.EnumMember(Value = @"workshop")]
  Workshop = 2,

  [System.Runtime.Serialization.EnumMember(Value = @"virtual")]
  Virtual = 3,

  RemoteInterviews = 20,
  Training = 22,
}
