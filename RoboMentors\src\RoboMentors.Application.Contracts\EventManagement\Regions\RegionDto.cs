﻿using System;
using System.Collections.Generic;
using System.Text;
using Volo.Abp.Application.Dtos;

namespace RoboMentors.EventManagement.Regions;

public class RegionDto : EntityDto<Guid>
{
  public string Country { get; set; } = string.Empty;
  public string RegionName { get; set; } = string.Empty;

  public String Region
  {
    get
    {
      return RegionName.Trim().Length > 0 ? RegionName.Trim() : Country.Trim();
    }
  }
}
