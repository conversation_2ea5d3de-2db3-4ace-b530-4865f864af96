﻿using Microsoft.AspNetCore.Components;
using RoboMentors.Identity.Users;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RoboMentors.Blazor.Components.Pages;

public partial class Index
{
  // Service Injections
  [Inject]
  protected NavigationManager Navigation { get; set; } = default!;

  // Page Variables
  private string HomePageQuestion = string.Empty;
  private string HomePageMasterImage = string.Empty;
  private string HomePageMasterTitle = string.Empty;
  private List<HomePageContent> HomePageContents = new();
  private bool IsDisabledJudge = false;

  // Methods
  protected override async Task OnInitializedAsync()
  {
    await GetHomePageContentAsync().ConfigureAwait(false);
  }

  private async Task GetHomePageContentAsync()
  {
    // Is User NOT Authenticated?
    if (CurrentUser.IsAuthenticated == false)
    {
      HomePageQuestion = "Home:Question:UnAuth";
      // Yes, give generic options
      HomePageContents = new List<HomePageContent>
      {
        new HomePageContent
        {
          Image = "/images/web/Judges.png",
          Title = "Role:Judge",
          Description = "Role:Judge:Description",
          Links = new()
        },
        new HomePageContent
        {
          Image = "/images/web/JudgeAdvisors.png",
          Title = "Role:JudgeAdvisor",
          Description = "Role:JudgeAdvisor:Description",
          Links = new()
        }
      };
      return;
    }
    // User is Authenticated
    // Is User a Disabled Judge?
    if (CurrentUser.IsDisabledJudge())
    {
      IsDisabledJudge = true;
      HomePageQuestion = string.Empty;
      HomePageContents = new List<HomePageContent>();
      return;
    }
    // User is not Disabled Judge
    // Is User in a EventManagement or Judging Role?
    if (CurrentUser.IsEventManagement() || CurrentUser.IsJudging())
    {
      // Yes, give options for Judging
      HomePageQuestion = string.Empty;
      HomePageMasterImage = "/images/web/WCJudges.jpg";
      HomePageMasterTitle = "Role:Judging";
      HomePageContents = new List<HomePageContent>
      {
        new HomePageContent
        {
          Image = "",
          Title = "Home:Volunteer:Title",
          Description = "",
          Links = new List<Link>
          {
            new Link { Href = "/volunteer", Label = "Home:Volunteer"}
          }
        },
        new HomePageContent
        {
          Image = "",
          Title = "Home:Preparations:Title",
          Description = "",
          Links = new List<Link>
          {
            new Link { Href = "judging/preparations", Label = "Home:Preparations"}
          }
        },
        new HomePageContent
        {
          Image = "",
          Title = "Home:Events:Title",
          Description = "",
          Links = new List<Link>
          {
            new Link { Href = "/events", Label = "Home:Events"}
          }
        },
      };
      return;
    }
    // Not a Judge
    // Is User an Admin?
    if (CurrentUser.Roles.Any(x => x.Contains("admin")) == true)
    {
      // Yes, give options for System Administrator
      HomePageContents = new List<HomePageContent>
      {
        new HomePageContent
        {
          Image = "/images/web/JudgeAdvisors.png",
          Title = "Role:Admin",
          Description = "Role:Admin:Description",
          Links = new List<Link>()
        }
      };
      return;
    }
    // Give Generic options
    HomePageContents = new List<HomePageContent>
    {
      new HomePageContent
      {
        Image = "/images/web/JudgeAdvisors.png",
        Title = "Role:Unknown",
        Description = "Role:Unknown:Description",
        Links = new List<Link>()
      }
    };
    return;
  }


  // Page Classes
  public class HomePageContent
  {
    public string Image { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public List<Link> Links { get; set; } = new List<Link>();
  }

  public class Link
  {
    public string Href { get; set; } = string.Empty;
    public string Label { get; set; } = string.Empty;
  }

}
