﻿using RoboMentors.Judging.Interviews;
using Shouldly;
using System;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Xunit;

namespace RoboMentors.Judging.Questions;

public abstract class QuestionAppService_Tests<TStartupModule> : RoboMentorsApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
  private readonly IQuestionAppService _questionAppService;

  protected QuestionAppService_Tests()
  {
    _questionAppService = GetRequiredService<IQuestionAppService>();
  }

  [Fact]
  public async Task Should_Get_AllQuestions_And_Get_FirstId()
  {
    // Act
    var result = await _questionAppService.GetDtosAsync();
    var totalCount = result.Count;
    var firstId = result[0].Id;
    var result2 = await _questionAppService.GetDtoAsync(firstId);

    // Assert
    totalCount.ShouldBeGreaterThan(0);
    result.ShouldContain(x => x.InterviewType == InterviewType.Initial);
    result.ShouldContain(x => x.InterviewType == InterviewType.Award);
    result2.Text.ShouldBeEquivalentTo(result[0].Text);
  }

  [Fact]
  public async Task Should_GetInitialInterviewQuestions()
  {
    // Act
    var result = await _questionAppService.GetDtosAsync(interviewType: InterviewType.Initial);
    var totalCount = result.Count;

    // Assert
    totalCount.ShouldBeGreaterThan(0);
    result.ShouldContain(x => x.InterviewType == InterviewType.Initial);
    result.ShouldNotContain(x => x.InterviewType == InterviewType.Award);
  }

  [Fact]
  public async Task Should_GetAwardInterviewQuestions()
  {
    // Act
    var result = await _questionAppService.GetDtosAsync(interviewType: InterviewType.Award);
    var totalCount = result.Count;

    // Assert
    totalCount.ShouldBeGreaterThan(0);
    result.ShouldNotContain(x => x.InterviewType == InterviewType.Initial);
    result.ShouldContain(x => x.InterviewType == InterviewType.Award);
  }

  [Fact]
  public async Task Should_GetDesignAwardInterviewQuestions()
  {
    // Act
    var result = await _questionAppService.GetDtosAsync(interviewType: InterviewType.Award, awardId: 2);
    var totalCount = result.Count;

    // Assert
    totalCount.ShouldBeGreaterThan(0);
    result.ShouldNotContain(x => x.InterviewType == InterviewType.Initial);
    result.ShouldContain(x => x.InterviewType == InterviewType.Award);
    result.ShouldContain(x => x.AwardId == 2);
  }

  [Fact]
  public async Task Should_Create_Update_Delete_Valid_Question()
  {
    // Add Act
    var addQuestion =
        new CreateUpdateQuestionDto
        {
          Id = new Guid(),
          InterviewType = InterviewType.Initial,
          AwardId = 1,
          Order = 1,
          NoteKeyword = string.Empty,
          Text = "Testing"
        };
    var addResult = await _questionAppService.CreateAsync(addQuestion);

    // Add Assert
    addResult.Success.ShouldBeTrue();
    addResult.Data?.Id.ShouldNotBe(Guid.Empty);
    addResult.Data?.NoteKeyword.ShouldBe(string.Empty);
    addResult.Data?.Text.ShouldBe("Testing");

    // Update Act
    var updateQuestion =
        new CreateUpdateQuestionDto
        {
          Id = new Guid(),
          InterviewType = InterviewType.Award,
          AwardId = 2,
          Order = 2,
          NoteKeyword = "Keyword update",
          Text = "Testing Update"
        };
    updateQuestion.Id = (Guid)addResult.Data?.Id;
    var updateResult = await _questionAppService.UpdateAsync(updateQuestion);

    // Update Assert
    updateResult.Success.ShouldBeTrue();
    updateResult.Data?.Id.ShouldNotBe(Guid.Empty);
    updateResult.Data?.InterviewType.ShouldBe(InterviewType.Award);
    updateResult.Data?.AwardId.ShouldBe(2);
    updateResult.Data?.Order.ShouldBe(2);
    updateResult.Data?.NoteKeyword.ShouldBe("Keyword update");
    updateResult.Data?.Text.ShouldBe("Testing Update");

    // Delete Act
    await _questionAppService.DeleteAsync(updateQuestion.Id);
    var deleteResult = await _questionAppService.GetDtoAsync(updateQuestion.Id);

    // Delete Assert
    deleteResult.Id.ShouldBe(Guid.Empty);
  }

}
