﻿using Microsoft.AspNetCore.Authorization;
using RoboMentors.Identity.Permissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Repositories;

namespace RoboMentors.Judging.Awards;

[Authorize(RoboMentorsPermissions.Awards.Default)]
public class AwardAppService : RoboMentorsAppService, IAwardAppService
{
  // Repository
  private readonly IAwardRepository _awardRepository;
  private readonly AwardManager _awardManager;

  public AwardAppService(
      IAwardRepository awardRepository,
      AwardManager awardManager)
  {
    _awardRepository = awardRepository;
    _awardManager = awardManager;
  }

  //*-----
  //* Get Award by Id
  //*-----
  private async Task<T> GetAsync<T>(int id)
  {
    // Do not use standard abp GetAsync as it throws exception if not found
    var query = await _awardRepository.GetQueryableAsync().ConfigureAwait(false);
    query = query.Where(x => x.Id == id).Select(x => x);
    var award = await AsyncExecuter.FirstOrDefaultAsync(query);
    return ObjectMapper.Map<Award, T>(award);
  }
  public async Task<AwardDto> GetDtoAsync(int id)
  {
    var award = await GetAsync<AwardDto>(id).ConfigureAwait(false);
    if (award == null) { return new AwardDto(); }
    return award;
  }

  //*-----
  //* Get Full List of Awards
  //*-----
  private async Task<List<T>> GetListAsync<T>()
  {
    var awards = await _awardRepository.GetListAsync().ConfigureAwait(false);
    return ObjectMapper.Map<List<Award>, List<T>>(awards);
  }
  public async Task<List<AwardDto>> GetDtosAsync()
  {
    var awardDtos = await GetListAsync<AwardDto>().ConfigureAwait(false);
    if (awardDtos == null) { return new List<AwardDto>(); }
    awardDtos = awardDtos.OrderBy(x => x.DefaultOrder).ToList();
    return awardDtos;
  }

  //*-----
  //* Get Paged List of Awards w/skipcount, maxresultcount, sorting & filtered
  //*-----
  public async Task<PagedResultDto<AwardDto>> GetPagedListAsync(GetListDto input)
  {
    // If sorting is not set, set default to Order
    if (input.Sorting.IsNullOrWhiteSpace())
    {
      input.Sorting = nameof(Award.DefaultOrder);
    }

    var awards = await _awardRepository.GetListAsync(
        input.SkipCount,
        input.MaxResultCount,
        input.Sorting,
        input.Filter
    );

    var totalCount = input.Filter == null
        ? await _awardRepository.CountAsync()
        : await _awardRepository.CountAsync(x => x.Keyword.Contains(input.Filter));

    return new PagedResultDto<AwardDto>(
        totalCount,
        ObjectMapper.Map<List<Award>, List<AwardDto>>(awards)
    );
  }

  public async Task<List<AwardKeywordLookupDto>> GetKeywordLookupAsync()
  {
    var awardKeywordLookupDtos = await GetListAsync<AwardKeywordLookupDto>().ConfigureAwait(false);
    if (awardKeywordLookupDtos == null) { return new List<AwardKeywordLookupDto>(); }
    awardKeywordLookupDtos = awardKeywordLookupDtos.OrderBy(x => x.Order).ToList();
    return awardKeywordLookupDtos;
  }

  //*-----
  //* Create new Award
  //*-----
  [Authorize(RoboMentorsPermissions.Awards.Create)]
  public async Task<ApiResult<AwardDto>> CreateAsync(CreateUpdateAwardDto input)
  {
    var result = await _awardManager.CreateAsync(
        id: input.Id,
        keyword: input.Keyword,
        abbrev: input.Abbrev,
        title: input.Title,
        program: input.Program,
        type: input.Type,
        description: input.Description,
        script: input.Script,
        icon: input.Icon,
        includeOnInitialRanking: input.IncludeOnInitialRanking,
        includeOnFollowUpRanking: input.IncludeOnFollowUpRanking,
        followUpInterviewDefaultMinutes: input.FollowUpInterviewDefaultMinutes,
        defaultOrder: input.DefaultOrder,
        viqOrder: input.VIQOrder
    );
    if (result.Success == true && result.Data != null)
    {
      await _awardRepository.InsertAsync(result.Data);
    }
    return ObjectMapper.Map<ApiResult<Award>, ApiResult<AwardDto>>(result);
  }

  //*-----
  //* Update Award
  //*-----
  [Authorize(RoboMentorsPermissions.Awards.Edit)]
  public async Task<ApiResult<AwardDto>> UpdateAsync(CreateUpdateAwardDto input)
  {
    var award = await _awardRepository.GetAsync(input.Id);
    var update = false;
    var result = new ApiResult<Award>();

    if (award.Keyword != input.Keyword)
    {
      result = await _awardManager.ChangeKeywordAsync(award, input.Keyword);
      if (result.Success == false)
      {
        return ObjectMapper.Map<ApiResult<Award>, ApiResult<AwardDto>>(result);
      }
      update = true;
    }

    if (update == true ||
        award.Abbrev != input.Abbrev ||
        award.Title != input.Title ||
        award.Program != input.Program ||
        award.Type != input.Type ||
        award.Description != input.Description ||
        award.Script != input.Script ||
        award.Icon != input.Icon ||
        award.IncludeOnInitialRanking != input.IncludeOnInitialRanking ||
        award.IncludeOnFollowUpRanking != input.IncludeOnFollowUpRanking ||
        award.FollowUpInterviewDefaultMinutes != input.FollowUpInterviewDefaultMinutes ||
        award.DefaultOrder != input.DefaultOrder ||
        award.VIQOrder != input.VIQOrder)
    {
      award.Abbrev = input.Abbrev;
      award.Title = input.Title;
      award.Program = input.Program;
      award.Type = input.Type;
      award.Description = input.Description;
      award.Script = input.Script;
      award.Icon = input.Icon;
      award.IncludeOnInitialRanking = input.IncludeOnInitialRanking;
      award.IncludeOnFollowUpRanking = input.IncludeOnFollowUpRanking;
      award.FollowUpInterviewDefaultMinutes = input.FollowUpInterviewDefaultMinutes;
      award.DefaultOrder = input.DefaultOrder;
      award.VIQOrder = input.VIQOrder;
      result.Data = await _awardRepository.UpdateAsync(award);
    }
    result.Success = true;
    return ObjectMapper.Map<ApiResult<Award>, ApiResult<AwardDto>>(result);
  }

  //*-----
  //* Delete Award
  //*-----
  [Authorize(RoboMentorsPermissions.Awards.Delete)]
  public async Task DeleteAsync(int id)
  {
    await _awardRepository.DeleteAsync(id);
  }

}
