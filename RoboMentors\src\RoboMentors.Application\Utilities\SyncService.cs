﻿using System;
using System.Threading.Tasks;

namespace RoboMentors.Utilities;

public class SyncService
{
  public static async Task<SyncResult> ExecuteAsync(Func<SyncResult, LoggingService, Task> executeAsync)
  {
    var results = new SyncResult();
    var log = new LoggingService();
    var start = DateTime.Now;
    log.WriteLine("Starting");

    try
    {
      await executeAsync(results, log).ConfigureAwait(false);
    }
    catch (Exception ex)
    {
        results.ExceptionDto = ExceptionDto.FromException(ex);
        log.WriteLine($"Exception was thrown with message '{results.ExceptionDto.MessagePath}'. StackTrace was '{results.ExceptionDto.StackTrace}'");
        results.Success = false;
    }
    var duration = new TimeSpan(DateTime.Now.Ticks - start.Ticks);
    results.RuntimeSeconds = duration.TotalSeconds;
    log.WriteLine($"Finished. Runtime = {duration.TotalSeconds:0.00} seconds");
    results.Log = log.Text;
    return results;
  }
}
