﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public partial class REWorldsDen
{
  [Newtonsoft.Json.JsonProperty("event", Required = Newtonsoft.Json.Required.Always)]
  public string EventSku { get; set; }

  [Newtonsoft.Json.JsonProperty("team", Required = Newtonsoft.Json.Required.Always)]
  public string TeamNumber { get; set; }

  [Newtonsoft.Json.JsonProperty("program", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string EventProgram { get; set; }

  [Newtonsoft.Json.JsonProperty("grade_level", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string GradeLevel { get; set; }

  [Newtonsoft.Json.JsonProperty("updated_at", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string UpdatedAt { get; set; }

  [Newtonsoft.Json.JsonProperty("language", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Language { get; set; }

  [Newtonsoft.Json.JsonProperty("disabled", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Disabled { get; set; }

  [Newtonsoft.Json.JsonProperty("notebook_link", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string NotebookLink { get; set; }

  [Newtonsoft.Json.JsonProperty("innovative_aspect_pages", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string InnovateAspectPages { get; set; }

  [Newtonsoft.Json.JsonProperty("innovative_aspect_description", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string InnovateAspectDescription { get; set; }
}
