﻿using RoboMentors.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace RoboMentors.EventManagement.Regions;

public class EfCoreRegionRepository :
    EfCoreRepository<RoboMentorsDbContext, Region>,
    IRegionRepository
{
  public EfCoreRegionRepository(
      IDbContextProvider<RoboMentorsDbContext> dbContextProvider)
      : base(dbContextProvider)
  {
  }


}
