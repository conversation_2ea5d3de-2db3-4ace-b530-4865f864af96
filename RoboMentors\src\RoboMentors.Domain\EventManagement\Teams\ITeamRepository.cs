﻿using RoboMentors.EventManagement.RobotEvents;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace RoboMentors.EventManagement.Teams;

public interface ITeamRepository : IBasicRepository<Team>
{
  Task<List<Team>> FindByProgramAsync(EventProgram program);
  Task<List<Team>> GetTeamsThatStartWithAsync(string? numberStartsWith, int limit = 200);
  Task<Dictionary<string, string>> GetProgramTeamNumberRegionsAsync(EventProgram? program = null);
  Task<Team?> FindByProgramTeamNumberAsync(EventProgram program, string teamNumber);
}
