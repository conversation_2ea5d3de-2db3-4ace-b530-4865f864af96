﻿using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RoboMentors.Managers.Apis.RobotEvents;

public interface IRobotEventsEventManager
{
  Task<List<REEvent>> GetEventsAsync(IEnumerable<int> id);
  Task<List<REEvent>> GetEventsAsync(DateTime start);
  Task<List<REEvent>> GetEventsAsync(IEnumerable<int> id, IEnumerable<string> sku, IEnumerable<int> team, IEnumerable<int> season, DateTime? start, DateTime? end, IEnumerable<EventLevel> level, bool? myEvents, IEnumerable<EventType> eventTypes);
}
