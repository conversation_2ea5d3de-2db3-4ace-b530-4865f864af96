using AutoMapper;
using RoboMentors.EventManagement.Regions;
using RoboMentors.Judging.Awards;
using RoboMentors.Judging.Questions;

namespace RoboMentors;

public class RoboMentorsApplicationAutoMapperProfile : Profile
{
  public RoboMentorsApplicationAutoMapperProfile()
  {
    /* You can configure your AutoMapper mapping configuration here.
      * Alternatively, you can split your mapping configurations
      * into multiple profile classes for a better organization. */

    ////*-----
    ////* General Mapping
    ////*-----
    //
    //
    ////*-----
    ////* Identity Mapping
    ////*-----
    ////* Roles
    //CreateMap<Volo.Abp.Identity.IdentityRole, RoleDto>();
    //
    ////* Users
    //CreateMap<Volo.Abp.Identity.IdentityUser, UserLookupDto>();
    //CreateMap<EventUserDto, UpdateUserProfileDto>();
    //CreateMap<UpdateUserProfileDto, EventUserDto>();
    //
    ////*-----
    ////* Event Management Mapping
    ////*-----
    ////* Event Entities
    //CreateMap<Event, EventDto>();
    //CreateMap<Event, EventLookupDto>();
    //CreateMap<EventDto, CreateUpdateEventREDto>();
    //CreateMap<EventDto, UpdateEventGenDto>();
    //CreateMap<EventDto, UpdateEventJdgDto>();
    //CreateMap<CreateUpdateEventREDto, Event>();
    //CreateMap<UpdateEventGenDto, Event>();
    //CreateMap<UpdateEventJdgDto, Event>();
    //CreateMap<REEventLookupDto, CreateUpdateEventREDto>();
    //CreateMap<RELocation, CreateUpdateEventREDto>();
    //CreateMap<ApiResult<Event>, ApiResult<EventDto>>();
    //
    ////* Event Awards
    //CreateMap<EventAward, EventAwardDto>();
    //CreateMap<EventAward, EventAwardLookupDto>();
    //CreateMap<EventAwardDto, UpdateEventAwardJdgDto>();
    //CreateMap<UpdateEventAwardJdgDto, EventAward>();
    //CreateMap<REAwardLookupDto, EventAward>();
    //
    ////* EventAwardDivisionWinner
    //CreateMap<EventAwardDivisionWinner, EventAwardDivisionWinnerDto>();
    //CreateMap<EventAwardDivisionWinnerDto, EventAwardDivisionWinner>();
    //
    ////* Event Divisions
    //CreateMap<EventDivision, EventDivisionDto>();
    //CreateMap<EventDivisionDto, EventDivision>();
    //CreateMap<EventPlanningDivision, EventPlanningDivisionDto>();
    //CreateMap<EventPlanningDivisionDto, EventPlanningDivision>();
    //CreateMap<EventPlanningDivisionDto, CreateUpdateEventPlanningDivisionDto>();
    //CreateMap<CreateUpdateEventPlanningDivisionDto, EventPlanningDivision>();
    //CreateMap<EventDivisionLists, EventDivisionListsDto>();
    //
    ////* Event Matches
    //CreateMap<EventMatch, EventMatchDto>();
    //CreateMap<EventMatchDto, EventMatch>();
    //CreateMap<REMatchLookupDto, EventMatch>();
    //CreateMap<REMatchLookupDto, EventMatchDto>();
    //
    ////* Event Rankings
    //CreateMap<EventRanking, EventRankingDto>();
    //CreateMap<EventRankingDto, EventRanking>();
    //CreateMap<RERankingLookupDto, EventRanking>();
    //CreateMap<RERankingLookupDto, EventRankingDto>();
    //
    ////* Event Skills
    //CreateMap<EventSkill, EventSkillDto>();
    //CreateMap<EventSkillDto, EventSkill>();
    //CreateMap<RESkillLookupDto, EventSkill>();
    //CreateMap<RESkillLookupDto, EventSkillDto>();
    //
    ////* Event Teams
    //CreateMap<EventTeam, EventTeamDto>();
    //CreateMap<EventTeamDto, EventTeam>();
    //CreateMap<RETeamLookupDto, EventTeam>();
    //
    ////* Event Users
    //CreateMap<EventUser, EventUserDto>();
    //CreateMap<EventUserWithDetails, EventUserDto>();
    //CreateMap<EventUserDto, MyEventUserDto>();
    //CreateMap<EventUserDto, UpdateEventUserGenDto>();
    //CreateMap<EventUserDto, UpdateEventUserJdgDto>();
    //CreateMap<UpdateEventUserGenDto, EventUser>();
    //CreateMap<UpdateEventUserJdgDto, EventUser>();
    //
    ////* RobotEvents Entities
    //CreateMap<REEvent, REEventLookupDto>();
    //CreateMap<REAward, REAwardLookupDto>();
    //CreateMap<RETeam, RETeamLookupDto>();
    //CreateMap<REMatch, REMatchLookupDto>();
    //CreateMap<RESkill, RESkillLookupDto>();
    //CreateMap<RERanking, RERankingLookupDto>();
    //
    ////* Picture Entities
    //CreateMap<Picture, PictureDto>();
    //CreateMap<PictureDto, CreatePictureDto>();
    //CreateMap<CreatePictureDto, Picture>();
    //
    //* Regions
    CreateMap<Region, RegionDto>();
    
    ////*-----
    ////* Judging Mapping
    ////*-----
    ////* Award Ranking Entities
    //CreateMap<AwardRanking, AwardRankingDto>();
    //CreateMap<AwardRankingDto, CreateUpdateAwardRankingDto>();
    //CreateMap<CreateUpdateAwardRankingDto, AwardRanking>();
    //
    //* Award Entities
    CreateMap<Award, AwardDto>();
    CreateMap<Award, AwardKeywordLookupDto>();
    CreateMap<AwardDto, CreateUpdateAwardDto>();
    CreateMap<CreateUpdateAwardDto, Award>();
    CreateMap<ApiResult<Award>, ApiResult<AwardDto>>();
    
    ////* ENRubric Entities
    //CreateMap<ENRubric, ENRubricDto>();
    //CreateMap<ENRubricDto, CreateUpdateENRubricDto>();
    //CreateMap<CreateUpdateENRubricDto, ENRubric>();
    //
    ////* Interviews Entities
    //CreateMap<Interview, InterviewDto>();
    //CreateMap<InterviewDto, CreateUpdateInterviewDto>();
    //CreateMap<CreateUpdateInterviewDto, Interview>();
    //
    //* Question Entities
    CreateMap<Question, QuestionDto>();
    CreateMap<QuestionDto, CreateUpdateQuestionDto>();
    CreateMap<CreateUpdateQuestionDto, Question>();
    CreateMap<ApiResult<Question>, ApiResult<QuestionDto>>();

  }
}
