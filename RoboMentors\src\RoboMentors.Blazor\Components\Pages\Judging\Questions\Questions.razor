﻿@page "/maintenance/questions"
@attribute [Authorize(RoboMentorsPermissions.Questions.Default)]
@using RoboMentors.Judging.Interviews;
@using RoboMentors.Judging.Questions;
@using RoboMentors.Localization;
@using RoboMentors.Identity.Permissions;
@using Volo.Abp.AspNetCore.Components.Web
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@inherits RoboMentorsComponentBase
@inject AbpBlazorMessageLocalizerHelper<RoboMentorsResource> LH

<PageHeader Title="@L["Menu:Maintenance:Judging:Questions"]" Toolbar="@Toolbar" BreadcrumbItems="@BreadcrumbItems"></PageHeader>
<Card>
	<CardHeader>
		<Row>
			<FieldLabel Class="mb-0">
				@L["Question:Filter"]
				<Button Size="Size.Small" Clicked="ClearFilters" Color="Color.Secondary">
					<Icon Name="IconName.Clear" IconSize="IconSize.Small"></Icon>@L["All"]
				</Button>
			</FieldLabel>
			<Column Class="mt-0" ColumnSize="ColumnSize.Is2">
				<Select TValue="string" SelectedValue="@FilterInterviewType" SelectedValueChanged="UpdateFilterInterviewType">
					<SelectItem TValue="string" Value="string.Empty">@L["Select:InterviewType"]</SelectItem>
					@foreach (var interviewType in Enum.GetValues(typeof(InterviewType)))
					{
						<SelectItem TValue="string" Value="@(interviewType.ToString())">
							@L[$"Enum:InterviewType:{(int)interviewType}"]
						</SelectItem>
					}
				</Select>
			</Column>
			<Column Class="mt-0" ColumnSize="ColumnSize.Is2">
				<Select TValue="string" SelectedValue="@FilterAwardKeyword" SelectedValueChanged="UpdateFilterAwardKeyword">
					<SelectItem TValue="string" Value="string.Empty">@L["Select:Award"]</SelectItem>
					@foreach (var award in AwardKeywordLookupDtos)
					{
						<SelectItem TValue="string" Value="@(award.Keyword)">
							@(award.Keyword)
						</SelectItem>
					}
				</Select>
			</Column>
			<Column Class="mt-0" ColumnSize="ColumnSize.Is8">
				<Addons>
					<Addon AddonType="AddonType.Body">
						<TextEdit Text="@FilterText" Placeholder="@L["Question:Filter:Text"]" TextChanged="UpdateFilterText" />
					</Addon>
					<Addon AddonType="AddonType.End">
						<Button Size="Size.Small" Clicked="ClearFilterText" Color="Color.Secondary">
							<Icon Name="IconName.Clear" IconSize="IconSize.Small"></Icon>
						</Button>
					</Addon>
				</Addons>
			</Column>
		</Row>
	</CardHeader>
	<CardBody Class="mt-0 pt-1">
		<DataGrid TItem="QuestionDto" Data="FilteredQuestionDtos" TotalItems="FilteredQuestionDtos.Count"
							ShowPager="@( FilteredQuestionDtos.Count > PageSize)" PagerPosition="DataGridPagerPosition.Bottom"
							ShowPageSizes="@( FilteredQuestionDtos.Count > PageSize)" PageSize="PageSize"
							Striped="false" Bordered="false" Hoverable="true" Responsive="true"
							SelectedRowChanged="@((row) => { OpenModalForEdit(row); } )">
			<LoadingTemplate><RadarSpinner></RadarSpinner></LoadingTemplate>
			<DataGridColumns>
				<DataGridColumn TItem="QuestionDto" Field="@nameof(QuestionDto.InterviewType)" Caption="@L["Question:InterviewType:Abbrev"]">
					<DisplayTemplate>@L[$"Enum:InterviewType:{(int)context.InterviewType}"]</DisplayTemplate>
				</DataGridColumn>
				<DataGridColumn TItem="QuestionDto" Field="@nameof(QuestionDto.AwardKeyword)" Caption="@L["Question:Award"]" />
				<DataGridColumn TItem="QuestionDto" Field="@nameof(QuestionDto.Order)" Caption="@L["Question:Order"]" />
				<DataGridColumn TItem="QuestionDto" Field="@nameof(QuestionDto.NoteKeyword)" Caption="@L["Question:NoteKeyword"]" />
				<DataGridColumn TItem="QuestionDto" Field="@nameof(QuestionDto.Text)" Caption="@L["Question:Text"]" />
			</DataGridColumns>
		</DataGrid>
	</CardBody>
</Card>

@* *** Create/Edit Modal *@
<Modal @ref="CreateUpdateModal" Animated="true">
	<ModalContent Size="ModalSize.Large" Centered="false">
		<Form>
			<ModalHeader>
				<ModalTitle>@L[$"Question:{FormMode.ToString()}"]</ModalTitle>
				<CloseButton Clicked="CloseModal" />
			</ModalHeader>
			<ModalBody>
				<Validations @ref="@ValidationsRef" Mode="ValidationMode.Auto" Model="@CUQuestionDto" ValidateOnLoad="false">
					<Fields>
						<Field ColumnSize="ColumnSize.Is3">
							<FieldLabel>@L["Question:InterviewType"]</FieldLabel>
							<Select TValue="InterviewType" @bind-SelectedValue="@CUQuestionDto.InterviewType" Disabled="@(FormMode != FormMode.New)" Autofocus>
								@foreach (int interviewTypeValue in Enum.GetValues(typeof(InterviewType)))
								{
									<SelectItem TValue="InterviewType" Value="@((InterviewType)interviewTypeValue)">
										@L[$"Enum:InterviewType:{interviewTypeValue}"]
									</SelectItem>
								}
							</Select>
						</Field>
						<Field ColumnSize="ColumnSize.Is3">
							<FieldLabel>@L["Question:Award"]</FieldLabel>
							<Select TValue="int" @bind-SelectedValue="@CUQuestionDto.AwardId">
								@foreach (var award in AwardKeywordLookupDtos)
								{
									<SelectItem TValue="int" Value="@(award.Id)">@(award.Keyword)</SelectItem>
								}
							</Select>
						</Field>
						<Validation MessageLocalizer="@LH.Localize">
							<Field ColumnSize="ColumnSize.Is2">
								<FieldLabel>@L["Question:Order"]</FieldLabel>
								<NumericEdit TValue="int" @bind-Value="@CUQuestionDto.Order" TextAlignment="TextAlignment.End" Min="0" Max="int.MaxValue">
									<Feedback><ValidationError>@L["Errors:Questions:Order"]</ValidationError></Feedback>
								</NumericEdit>
							</Field>
						</Validation>
					</Fields>
					<Validation Validator="ValidationRule.IsNotEmpty" MessageLocalizer="@LH.Localize">
						<Field>
							<FieldLabel>@L["Question:Text"]</FieldLabel>
							<MemoEdit @bind-Text="@CUQuestionDto.Text" Rows="2" Placeholder="@L["Question:Text:Placeholder"]">
								<Feedback><ValidationError>@L["Errors:Questions:Text"]</ValidationError></Feedback>
							</MemoEdit>
						</Field>
					</Validation>
					@if (CUQuestionDto.InterviewType == InterviewType.RemoteInitial)
					{
						<Validation MessageLocalizer="@LH.Localize">
							<Field>
								<FieldLabel>@L["Question:NoteKeyword"]</FieldLabel>
								<MemoEdit @bind-Text="@CUQuestionDto.NoteKeyword" Rows="1" Placeholder="@L["Question:NoteKeyword:Placeholder"]">
									<Feedback><ValidationError></ValidationError></Feedback>
								</MemoEdit>
							</Field>
						</Validation>
					}
				</Validations>
			</ModalBody>
			<ModalFooter>
				@if (CanDelete && (FormMode == FormMode.Edit || FormMode == FormMode.Delete))
				{
					<Button Color="Color.Secondary" Clicked="@(async () => { await DeleteAsync().ConfigureAwait(false); })">@L["Delete"]</Button>
				}
				<Button Color="Color.Secondary" Clicked="CloseModal">@L["Cancel"]</Button>
				@if (IsReadOnly == false && (CanCreate || CanEdit))
				{
					<Button Color="Color.Primary" Clicked="@(async () => { await SaveAsync().ConfigureAwait(false); })">@L["Save"]</Button>
				}
			</ModalFooter>
		</Form>
	</ModalContent>
</Modal>
