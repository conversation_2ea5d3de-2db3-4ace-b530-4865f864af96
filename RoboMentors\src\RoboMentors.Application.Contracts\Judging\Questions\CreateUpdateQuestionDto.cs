﻿using RoboMentors.Judging.Interviews;
using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace RoboMentors.Judging.Questions;

public class CreateUpdateQuestionDto : AuditedEntityDto<Guid>
{
  [Required]
  public InterviewType InterviewType { get; set; }

  [Required]
  public int AwardId { get; set; }

  [Required]
  [Range(0, 1000)]
  public int Order { get; set; }

  public string NoteKeyword { get; set; } = string.Empty;

  [Required]
  public string Text { get; set; } = string.Empty;
}
