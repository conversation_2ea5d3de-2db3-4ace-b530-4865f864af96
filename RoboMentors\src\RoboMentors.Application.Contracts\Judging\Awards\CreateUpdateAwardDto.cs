﻿using RoboMentors.EventManagement.EventAwards;
using RoboMentors.EventManagement.RobotEvents;
using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Application.Dtos;

namespace RoboMentors.Judging.Awards;

public class CreateUpdateAwardDto : AuditedEntityDto<int>
{
  // Id must be unique

  [Required]
  [StringLength(AwardConsts.KeywordMaxLength)]
  // must also be unique
  public string Keyword { get; set; } = string.Empty;

  [Required]
  [StringLength(AwardConsts.AbbrevMaxLength)]
  public string Abbrev { get; set; } = string.Empty;

  [Required]
  public string Title { get; set; } = string.Empty;

  [Required]
  public EventProgram Program { get; set; } = EventProgram.All;

  [Required]
  public AwardType Type { get; set; } = AwardType.Judged;

  [Required]
  public string Description { get; set; } = string.Empty;

  [Required]
  public string Script { get; set; } = string.Empty;

  [Required]
  public string Icon { get; set; } = string.Empty;

  public bool IncludeOnInitialRanking { get; set; }

  public bool IncludeOnFollowUpRanking { get; set; }

  [Required]
  [Range(AwardConsts.FollowUpInterviewMinutesMin, AwardConsts.FollowUpInterviewMinutesMax)]
  public int FollowUpInterviewDefaultMinutes { get; set; } = AwardConsts.FollowUpInterviewMinutesDefault;

  [Required]
  [Range(AwardConsts.OrderMin, AwardConsts.OrderMax)]
  public int DefaultOrder { get; set; } = AwardConsts.OrderMax;

  [Required]
  [Range(AwardConsts.OrderMin, AwardConsts.OrderMax)]
  public int VIQOrder { get; set; } = AwardConsts.OrderMax;

}
