{"id": "41ab87a4-9aae-4d5c-942f-e26e49f70034", "template": "app", "versions": {"AbpFramework": "9.3.0", "AbpStudio": "1.1.2", "TargetDotnetFramework": "net9.0", "AbpCommercial": "9.3.0", "LeptonX": "4.3.0"}, "modules": {"RoboMentors": {"path": "RoboMentors.abpmdl"}}, "runProfiles": {"Default": {"path": "etc/abp-studio/run-profiles/Default.abprun.json"}}, "options": {"httpRequests": {"ignoredUrls": []}}, "creatingStudioConfiguration": {"template": "app", "createdAbpStudioVersion": "1.1.2", "tiered": "false", "runInstallLibs": "true", "useLocalReferences": "false", "multiTenancy": "false", "includeTests": "true", "kubernetesConfiguration": "false", "uiFramework": "blazor-server", "mobileFramework": "none", "distributedEventBus": "none", "databaseProvider": "ef", "runDbMigrator": "true", "databaseManagementSystem": "sqlserver", "separateTenantSchema": "false", "createInitialMigration": "true", "theme": "leptonx-lite", "themeStyle": "", "themeMenuPlacement": "", "publicWebsite": "false", "socialLogin": "false", "selectedLanguages": ["English", "Español"], "defaultLanguage": "English", "createCommand": "abp new RoboMentors -t app --ui-framework blazor-server --database-provider ef --database-management-system sqlserver --theme leptonx-lite --without-cms-kit --dont-run-bundling --no-multi-tenancy --no-social-logins -no-gdpr -no-openiddict-admin-ui -no-audit-logging -no-file-management -no-language-management -no-text-template-management"}}