﻿@using System.Globalization
@using Microsoft.Extensions.Hosting
@using Volo.Abp.AspNetCore.Components.Server.LeptonXLiteTheme.Bundling
@using Volo.Abp.Localization
@using Volo.Abp.AspNetCore.Components.Web.Theming.Bundling
@inject IHostEnvironment Env
@{
  var rtl = CultureHelper.IsRtl ? "rtl" : string.Empty;
}

<!DOCTYPE html>
<html lang="@CultureInfo.CurrentCulture.Name" dir="@rtl">
<head>

  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>RoboMentors</title>
  <base href="/" />

  <AbpStyles BundleName="@BlazorLeptonXLiteThemeBundles.Styles.Global" />

  <link href="RoboMentors.Blazor.styles.css" rel="stylesheet"/>

  <HeadOutlet @rendermode="InteractiveServer" />

</head>
<body class="abp-application-layout @rtl">

  <Routes @rendermode="InteractiveServer" />

  @* 11/21/24 - MEU - Add Freshdesk Help widget *@
  <script>
    window.fwSettings = {
      'widget_id': 157000000240
    };
    !function () { if ("function" != typeof window.FreshworksWidget) { var n = function () { n.q.push(arguments) }; n.q = [], window.FreshworksWidget = n } }()
  </script>
  <script type='text/javascript' src='https://widget.freshworks.com/widgets/157000000240.js' async defer></script>
  @* 11/21/24 - MEU - Add Freshdesk Help widget *@


  <div id="blazor-error-ui">
    @if (Env.IsDevelopment())
    {
      <text>An unhandled exception has occurred. See browser dev tools for details.</text>
    }
    else if (Env.IsStaging() || Env.IsProduction())
    {
      <text>An error has occurred. This application may no longer respond until reloaded.</text>
    }
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
  </div>
    
  <AbpScripts BundleName="@BlazorLeptonXLiteThemeBundles.Scripts.Global" />

  <script src="_framework/blazor.web.js"></script>

</body>
</html>
