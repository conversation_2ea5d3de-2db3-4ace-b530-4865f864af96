﻿using Newtonsoft.Json;
using System.CodeDom.Compiler;
using System.Collections.Generic;

namespace RoboMentors.Managers.Apis.RobotEvents;

[GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public partial class PaginatedItem<T>
{
  [JsonProperty("meta", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
  public PageMeta Meta { get; set; }

  [JsonProperty("data", Required = Required.DisallowNull, NullValueHandling = NullValueHandling.Ignore)]
  public ICollection<T> Data { get; set; }

  private IDictionary<string, object> _additionalProperties = new Dictionary<string, object>();

  [JsonExtensionData]
  public IDictionary<string, object> AdditionalProperties
  {
    get { return _additionalProperties; }
    set { _additionalProperties = value; }
  }
}
