param(
    [string]$DatabaseName = "RECF-RoboMentorsV2",
    [string]$ServerInstance = "(LocalDb)\MSSQLLocalDB",
    [string]$BackupDirectory = "C:\Temp\RoboMentors-Migration\DatabaseBackups"
)

try {
    Write-Host "Starting database backup for upgrade..." -ForegroundColor Green
    
    # Install SqlServer module if needed
    if (-not (Get-Module -ListAvailable -Name SqlServer)) {
        Write-Host "Installing SqlServer PowerShell module..." -ForegroundColor Yellow
        Install-Module -Name SqlServer -Force -AllowClobber -Scope CurrentUser
    }
    
    Import-Module SqlServer -Force
    
    # Create backup directory
    if (-not (Test-Path $BackupDirectory)) {
        Write-Host "Creating backup directory: $BackupDirectory" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $BackupDirectory -Force | Out-Null
    }
    
    # Generate timestamped backup filename
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFileName = "${DatabaseName}_${timestamp}.bak"
    $backupPath = Join-Path $BackupDirectory $backupFileName
    
    Write-Host "Backing up '$DatabaseName' to: $backupPath" -ForegroundColor Cyan
    
    # Check SQL Server edition to determine if compression is supported
    $editionQuery = "SELECT SERVERPROPERTY('Edition') as Edition"
    $edition = Invoke-Sqlcmd -ServerInstance $ServerInstance -Query $editionQuery
    $isExpress = $edition.Edition -like "*Express*"
    
    if ($isExpress) {
        Write-Host "Detected SQL Server Express - using backup without compression" -ForegroundColor Yellow
        
        # Use T-SQL for Express (no compression support)
        $backupQuery = "BACKUP DATABASE [$DatabaseName] TO DISK = '$backupPath' WITH COPY_ONLY, INIT;"
        Invoke-Sqlcmd -ServerInstance $ServerInstance -Query $backupQuery
    }
    else {
        Write-Host "Using backup with compression" -ForegroundColor Yellow
        
        # Use PowerShell cmdlet with compression for full SQL Server
        Backup-SqlDatabase -ServerInstance $ServerInstance `
                           -Database $DatabaseName `
                           -BackupFile $backupPath `
                           -CopyOnly `
                           -CompressionOption On
    }
    
    # Verify backup was created and report results
    if (Test-Path $backupPath) {
        $backupFile = Get-Item $backupPath
        Write-Host "✅ Backup completed successfully!" -ForegroundColor Green
        Write-Host "   File: $($backupFile.FullName)" -ForegroundColor Gray
        Write-Host "   Size: $([math]::Round($backupFile.Length / 1MB, 2)) MB" -ForegroundColor Gray
        Write-Host "   Created: $($backupFile.LastWriteTime)" -ForegroundColor Gray
        
        # Verify backup integrity
        Write-Host "Verifying backup integrity..." -ForegroundColor Yellow
        Invoke-Sqlcmd -ServerInstance $ServerInstance -Query "RESTORE VERIFYONLY FROM DISK = '$backupPath'" | Out-Null
        Write-Host "✅ Backup verification passed!" -ForegroundColor Green
    }
    else {
        throw "Backup file was not created at expected location: $backupPath"
    }
}
catch {
    Write-Host "❌ Backup failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}