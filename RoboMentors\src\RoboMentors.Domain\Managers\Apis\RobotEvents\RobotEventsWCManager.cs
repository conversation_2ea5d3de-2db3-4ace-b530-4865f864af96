﻿using Newtonsoft.Json;
using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace RoboMentors.Managers.Apis.RobotEvents
{
	public class RobotEventsWCManager : DomainService, IRobotEventsWCManager
	{
		private readonly HttpClient _httpClient = null;
		private readonly string _baseUrl;

		public RobotEventsWCManager()
		{
			_httpClient = new HttpClient();
			_httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(RobotEventConsts.REScheme, RobotEventConsts.REWCKey);
			_baseUrl = "https://www.robotevents.com/api/";  
		}

		public static RobotEventsWCManager Create()
		{
			return new RobotEventsWCManager();
		}

		protected virtual async Task<T> GetItemsAsync<T>(string url, CancellationToken cancellationToken) where T : class, new()
		{
			T list = new T();
			var client = _httpClient;
			var disposeClient = false;

			try
			{
				// construct the url
				var notDone = true;
				do
				{
					using (var request = new HttpRequestMessage())
					{
						request.Method = HttpMethod.Get;
						request.Headers.Accept.Add(MediaTypeWithQualityHeaderValue.Parse("application/json"));
						request.Headers.Add("Connection", "keep-alive");
						request.RequestUri = new Uri(url, UriKind.RelativeOrAbsolute);

						HttpResponseMessage response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
						var disposeResponse = true;
						try
						{
							var headers = Enumerable.ToDictionary(response.Headers, h => h.Key, h => h.Value);
							if (response.Content != null && response.Content.Headers != null)
							{
								foreach (var item in response.Content.Headers)
									headers[item.Key] = item.Value;
							}

							var status = (int)response.StatusCode;
							if (status == 200)
							{
								var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
								try
								{
									list = JsonConvert.DeserializeObject<T>(responseText);
									notDone = false;
								} catch (Exception ex) 
								{
                  throw new ApiException($"Issue with JsonConvert.", status, responseText, headers, ex);
                }
              }
							else if (status == 429)
							{
								// we exceeded the rate limit. sleep for a while
								await Task.Delay(10000);
							}
							else
							{
								var responseData = response.Content == null ? null : await response.Content.ReadAsStringAsync().ConfigureAwait(false);
								throw new ApiException($"The HTTP status code of the response was not expected ({status}).", status, responseData, headers, null);
							}
						}
						finally
						{
							if (disposeResponse)
								response.Dispose();
						}
					} // using
				} while (notDone);
			}
			finally
			{
				if (disposeClient)
					client.Dispose();
			}
			return list;
		}

		public async Task<List<REJudgingSchedule>> GetREJudgingSchedulesAsync(string environment)
		{
			// 02/03/25 - MEU - changed to new api calls - current vs previous
			var version = "current";
      var url = $"{_baseUrl}judgingScheduled/{version}";
			var items = await GetItemsAsync<List<REJudgingSchedule>>(url, CancellationToken.None).ConfigureAwait(false);
			if ((items == null || items.Count < 1) && environment.Contains("prod") == false)
			{
        version = "previous";
        url = $"{_baseUrl}judgingScheduled/{version}";
        items = await GetItemsAsync<List<REJudgingSchedule>>(url, CancellationToken.None).ConfigureAwait(false);
      }
			return items;
    }

    public async Task<List<REWorldsDen>> GetREWorldsDenAsync()
    {
      var url = $"{_baseUrl}worldsDen";
      var dictionary = await GetItemsAsync<Dictionary<string, REWorldsDen>>(url, CancellationToken.None).ConfigureAwait(false);
			return dictionary.Values.ToList();
    }

    public async Task<List<REWorldsDen>> GetREEventDensAsync(string sku)
    {
      var url = $"{_baseUrl}eventDen/{sku}";
      var dictionary = await GetItemsAsync<Dictionary<string, REWorldsDen>>(url, CancellationToken.None).ConfigureAwait(false);
      return dictionary.Values.ToList();
    }

		public async Task<List<RETeamAward>> GetRETeamAwardsAsync(string program)
		{
      var url = $"{_baseUrl}teamAwards?program={program}";
      return await GetItemsAsync<List<RETeamAward>>(url, CancellationToken.None).ConfigureAwait(false);
    }

    public async Task<List<REMasterSkill>> GetREMasterSkillsAsync(string program)
    {
      var url = $"{_baseUrl}masterSkills?program={program}";
      return await GetItemsAsync<List<REMasterSkill>>(url, CancellationToken.None).ConfigureAwait(false);
    }
  }
}
