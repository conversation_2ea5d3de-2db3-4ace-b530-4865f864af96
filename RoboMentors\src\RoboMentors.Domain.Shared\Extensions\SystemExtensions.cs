﻿using RoboMentors.EventManagement.RobotEvents;
using RoboMentors.EventManagement.Teams;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace RoboMentors.Extensions
{
  public static class SystemExtensions
  {
	   //
		 // System.String extensions 
		 //

    public static string DefaultIfNull(this string value, string defaultValue)
    {
      return value != null ? value : defaultValue;
    }

    public static string IfNullOrEmpty(this string value, string defaultValue)
    {
      return string.IsNullOrEmpty(value) == false ? value : defaultValue;
    }

    public static string IfNullOrWhiteSpace(this string value, string defaultValue)
    {
      return string.IsNullOrWhiteSpace(value) == false ? value : defaultValue;
    }

	  public static void IfNullOrWhiteSpace(this string value, Action action)
	  {
      if (string.IsNullOrWhiteSpace(value) == true)
          {
              action();
          }
	  }

    public static async Task IfNullOrWhiteSpaceAsync(this string value, Func<Task> actionAsync)
    {
        if (string.IsNullOrWhiteSpace(value) == true)
        {
	      await actionAsync().ConfigureAwait(false); ;
        }
    }

    public static DateTime ToDateTime(this string item)
		{
			if (item.IsNullOrEmpty())
			{
				return DateTime.MinValue;
			}
			var success = DateTime.TryParse(item, out var result);
			if (success == false)
			{
				return DateTime.MinValue;
			}
			return result;
		}

		public static DateTimeOffset ToDateTimeOffset(this string item, string? format = "d-MMM-yyyy HH:mm", string? offset = null)
		{
			var defaultValue = DateTimeOffset.MinValue;
			CultureInfo provider = CultureInfo.InvariantCulture;
			if (item.IsNullOrEmpty())
			{
				return defaultValue;
			}
			var parts = item.Split(' ');
			if (parts.Length < 2)
			{
				return defaultValue;
			}
			var dateTimePart = string.Join(" ", parts.Take(parts.Length - 1));
			var timeZonePart = parts.Length < 3 ? "GMT" : parts.Last();
			var timeZoneText =
						timeZonePart.IsIn("GMT") ? "Greenwich Mean Time" :
            timeZonePart.IsIn("EST", "EDT") ? "Eastern Standard Time" :
						timeZonePart.IsIn("CST", "CDT") ? "Central Standard Time" :
						"Eastern Standard Time";
			try
			{
				// Parse the date and time
				DateTime parsedDate = DateTime.ParseExact(dateTimePart, format, provider);

				TimeSpan timeSpanOffset = new();
				string timeZoneId = string.Empty;
				// Offset supplied?
				if (offset == null)
				{
					// Adjust the TimeZoneInfo according to your needs
					TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneText);
					bool isDaylight = timeZone.IsDaylightSavingTime(parsedDate);
					timeZoneId = isDaylight ? timeZoneText.Replace("Standard", "Daylight") : timeZoneText;
					timeSpanOffset = timeZone.GetUtcOffset(parsedDate);
					// Apply the time zone to get the DateTimeOffset
					if ((timeZonePart == "GMT") ||
							(timeZonePart == "EST" && timeZoneId == "Eastern Standard Time") ||
							(timeZonePart == "EDT" && timeZoneId == "Eastern Daylight Time") ||
							(timeZonePart == "CST" && timeZoneId == "Central Standard Time") ||
							(timeZonePart == "CDT" && timeZoneId == "Central Daylight Time"))
					{
						return new DateTimeOffset(parsedDate, timeSpanOffset);
					}
					else
					{
						return defaultValue;
					}
				}
				// Offset Supplied
        var offsetString = offset.Substring(3, offset.Length - 3);
				var offsetParts = offsetString.Split(':');
				var hours = offsetParts.Length > 0 ? int.Parse(offsetParts[0]) : 0;
				parsedDate = parsedDate.AddHours(hours);
				timeSpanOffset = TimeSpan.FromHours(hours);
				var parsedDateTimeOffset = new DateTimeOffset(parsedDate, timeSpanOffset);
				return parsedDateTimeOffset;
			} catch (Exception)
			{
				return defaultValue;
			}
		}

		public static string ToFormatedStartEndDates(this DateTimeOffset start, DateTimeOffset end, string startTimezoneTag = "", string endTimezoneTag = "", string format = "ddd MM/dd/yyyy hh:mm tt", bool includeBothTimes = false)
		{
      var startFormat = start.Date == end.Date || includeBothTimes ? format : format.Replace("hh:mm tt", string.Empty).Replace("HH:mm", string.Empty).Trim();
      var startTzTag = startTimezoneTag == endTimezoneTag ? string.Empty : startTimezoneTag;
			var startText = $"{start.ToString(startFormat)} {startTzTag}".Trim();
      var endFormat =
					end.Date == start.Date ? format.Replace("ddd", string.Empty).Replace("MM/dd", string.Empty).Replace("/yyyy", string.Empty).Trim() :
          end.Year == start.Year ? startFormat.Replace("/yyyy", string.Empty).Trim() : startFormat;
      var endText = $"{end.ToString(endFormat)} {endTimezoneTag}".Trim();
      var text = $"{startText} - {endText}";
      return text;
    }

    public static DateTime? ToNullableDateTime(this string item)
		{
			if (item.IsNullOrEmpty())
			{
				return null;
			}
			var success = DateTime.TryParse(item, out DateTime result);
			if (success == false)
			{
				return null;
			}
			return result;
		}

		public static int? ToNullableInt(this string item)
    {
      if (item.IsNullOrEmpty() )
      {
        return null;
      } 
      var success = Int32.TryParse(item, out var result);
      if ( success == false )
      {
        return null;
      }
      return result;
    }

		public static int ToInt(this string item, int defaultNullOrError = 0)
		{
			if (item.IsNullOrEmpty())
			{
				return defaultNullOrError;
			}
			var success = Int32.TryParse(item, out var result);
			if (success == false)
			{
				return defaultNullOrError;
			}
			return result;
		}

		public static double ToDouble(this string item, double defaultNullOrError = 0) 
		{
			if (item.IsNullOrEmpty())
			{
				return defaultNullOrError;
			}
			var success = Double.TryParse(item, out var result);
			if (success == false) 
			{
				return defaultNullOrError;
			}
			return result;
		}

		public static double ZeroIfEpsilon(this double value, double defaultIfEpsilon = 0)
		{
			return value == double.Epsilon ? defaultIfEpsilon : value;
		}

		public static string ToStringIfEpsilon(this double value, string defaultIfEpsilon = "NS")
		{
			return value == double.Epsilon ? defaultIfEpsilon : value.ToString();
		}

		public static double CalcAverage(this double total, double count, int digits = 3)
		{
      var avgScore = count == 0 ? 0 : total / count;
      return Math.Round(avgScore, digits);
    }

    public static bool ToBool(this string item, bool defaultNullOrError = false)
    {
      if (item.IsNullOrEmpty() == true)
      {
        return defaultNullOrError;
      }
      return item.ToLower().IsIn("yes", "true");
    }

    public static string IfNull(this string value, string defaultValue)
    {
      var returnValue = defaultValue;

      if (value != null)
      {
        returnValue = value;
      }

      return returnValue;
    }

    public static bool IsContainedIn(this string item, IEnumerable<string> collection)
    {
      // Check for null inputs
      if (string.IsNullOrEmpty(item) || collection == null)
      {
        return false;
      }

      // Perform a case-insensitive check to see if the item is in the collection
      return collection.Any(x => string.Equals(x, item, StringComparison.OrdinalIgnoreCase));
    }

    //
    // System.DateTimeOffset extensions
    //
    public static int CompareDatesToServerOffset(this DateTimeOffset start, DateTimeOffset? end)
		{
			var today = DateTimeOffset.Now;
			var offset = start.Offset.TotalHours - today.Offset.TotalHours;
			today = today.AddHours(offset);
			end = end.HasValue ? end : start;
			if (today.Date < start.Date)
			{
				return -1;
			} else if ( today.Date.IsBetween(start.Date, end.Value.Date) )
			{
				return 0;
			} else
			{
				return 1;
			}
		}

		public static string ToFullDateAtTime(this DateTimeOffset value)
		{
			return $"{value.ToString("dddd, MMMM d, yyyy")} at {value.ToString("hh:mm tt")}";
    }

		public static DateTimeOffset ToNextHalfHour(this DateTimeOffset value)
		{
			var minutesToAdd = (30 - value.Minute % 30) % 30;
			value = value.AddMinutes(minutesToAdd);
			return new DateTimeOffset(value.Year, value.Month, value.Day, value.Hour, value.Minute, 0, value.Offset);
		}

		// 
		// File extensions
		//
    public static string GetBaseFileName(this string filePath)
    {
      if (string.IsNullOrWhiteSpace(filePath))
      {
        throw new ArgumentException("File path is null or whitespace.");
      }

      return Path.GetFileName(filePath);
    }

    public static string ToBase64(this string filePath)
    {
      if (string.IsNullOrWhiteSpace(filePath))
      {
        throw new ArgumentException("File path is null or whitespace.");
      }

      if (!File.Exists(filePath))
      {
        throw new FileNotFoundException("File does not exist.", filePath);
      }

      byte[] fileBytes = File.ReadAllBytes(filePath);
      return Convert.ToBase64String(fileBytes);
    }

		public static string AddNewLine(this string value, string line, string separator = "\n")
    {
      return value.IsNullOrEmpty() ? line : value + separator + line;
    }

		public static string ToProgramTeamNumber(this EventProgram program, string teamNumber, char separator = TeamConsts.ProgramTeamNumberSeparator)
		{
			return $"{program.ToString()}{separator}{teamNumber.Trim()}";
		}

		public static EventProgram GetEventProgram(this string programTeamNumber, char separator = TeamConsts.ProgramTeamNumberSeparator)
		{
			var parts = programTeamNumber.Split(separator);
			return parts.Length == 0 ? EventProgram.Error :
					parts[0].ToEventProgram();
		}

		public static string GetTeamNumber(this string programTeamNumber, char separator = TeamConsts.ProgramTeamNumberSeparator)
		{
      var parts = programTeamNumber.Split(separator);
      return parts.Length < 2 ? "Error" : parts[1].Trim();
    }

    public static bool IncludeInFilter(this string text, string filterText)
		{
			var searchWords = filterText.IsNullOrWhiteSpace() ? new List<string>() : filterText.Split(" ").ToList();
			return 
					string.IsNullOrEmpty(filterText) ||
					searchWords.All(word => text.ToLower().Contains(word.ToLower()));
		}

  }
}
