﻿using RoboMentors.EventManagement.RobotEvents;
using RoboMentors.Utilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace RoboMentors.EventManagement.Teams;

public interface ITeamAppService : IApplicationService
{
  Task<List<TeamDto>> GetTeamsAsync();
  Task<List<TeamDto>> GetTeamsThatStartsWithAsync(string teamNumber, int limit = 200);
  Task<List<TeamDto>> GetTeamsAsync(EventProgram program);
  Task<Dictionary<string, string>> GetProgramTeamNumbersAsync(EventProgram? program = null);
  Task<TeamDto?> FindTeamByProgramTeamNumberAsync(EventProgram program, string teamNumber);
  Task<SyncResult> RequestSyncTeamsAsync(List<EventProgram>? programs = null);
  Task<SyncJobResponse> SyncTeamsAsync();
  Task<SyncResult> GetSyncTeamsStatusAsync(string jobId);
}
