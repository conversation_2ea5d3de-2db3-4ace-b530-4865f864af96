﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RoboMentors.Extensions;

public static class SystemCollectionsGeneric
{
  public static string ToDelimitedString(this List<string> strings, string delimiter = ", ")
  {
    if (strings == null || strings.Count == 0)
    {
      return string.Empty;
    }
    var sb = new StringBuilder();
    foreach (var s in strings)
    {
      sb.Append(s);
      sb.Append(delimiter);
    }
    return sb.ToString().TrimEnd(delimiter.ToCharArray());
  }

  public static bool ContainsAny(this IEnumerable<string> source, IEnumerable<string> strings)
  {
    if (source == null || !source.Any() || strings == null || !strings.Any())
    {
      return false;
    }

    return strings.Any(thisString => source.Contains(thisString, StringComparer.OrdinalIgnoreCase));
  }
}
