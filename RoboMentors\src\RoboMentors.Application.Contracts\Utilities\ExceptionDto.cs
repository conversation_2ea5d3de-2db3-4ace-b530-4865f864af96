﻿using System;

namespace RoboMentors.Utilities;

public class ExceptionDto
{
  public string Message { get; set; }
  public string StackTrace { get; set; }
  public string ExceptionTypeName { get; set; }
  public string MessagePath { get; set; }
  public ExceptionDto InnerExceptionDto { get; set; }

  public static ExceptionDto FromException(Exception ex)
  {
    if (ex == null)
    {
      return null;
    }

    var innerExceptionDto = FromException(ex.InnerException);

    var messagePath = ex.Message;
    if (innerExceptionDto != null)
    {
      messagePath += " => " + innerExceptionDto.MessagePath;
    }

    return new ExceptionDto
    {
      Message = ex.Message,
      StackTrace = ex.StackTrace,
      ExceptionTypeName = ex.GetType().FullName,
      MessagePath = messagePath,
      InnerExceptionDto = innerExceptionDto
    };
  }
}
