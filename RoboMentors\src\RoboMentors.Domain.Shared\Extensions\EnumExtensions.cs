﻿using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;

namespace RoboMentors.Extensions
{
  public static class EnumExtensions
  {
    public static IDictionary<int, String> GetEnumValueNames(this Type type)
    {
      var names = type.GetFields(BindingFlags.Public | BindingFlags.Static)
          .Select(f => f.GetCustomAttribute<DisplayAttribute>()?.Name ?? f.Name);

      var values = Enum.GetValues(type).Cast<int>();

      var dictionary = names.Zip(values, (n, v) => new KeyValuePair<int, string>(v, n))
          .ToDictionary(kv => kv.Key, kv => kv.Value);

      return dictionary;
    }

    public static string GetDisplayName(this Enum enumValue)
    {
      return enumValue
            .GetType()
            .GetMember(enumValue.ToString())
            .First()
            .GetCustomAttribute<DisplayAttribute>()
            .GetName();
    }

    public static Grade ToGrade(this string gradeString)
    {
      gradeString = gradeString.Replace(" ", "_");
      gradeString = gradeString == "Elementary" ? "Elementary_School" : gradeString;
      var success = Enum.TryParse(gradeString, out Grade result);
      if (success == false)
      {
        return Grade.Error;
      }
      return (Grade)Enum.Parse(typeof(Grade), gradeString);
    }

    public static EventProgram ToEventProgram(this string eventProgramString)
    {
      var success = Enum.TryParse(eventProgramString, out EventProgram result);
      if (success == false)
      {
        return EventProgram.Error;
      }
      return result;
    }

  }
}
