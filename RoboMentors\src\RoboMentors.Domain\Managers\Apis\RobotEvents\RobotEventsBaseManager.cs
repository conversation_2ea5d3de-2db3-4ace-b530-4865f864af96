﻿using Newtonsoft.Json;
using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Runtime.Serialization;
using System.Threading;
using System.Threading.Tasks;
using Volo.Abp.Domain.Services;

namespace RoboMentors.Managers.Apis.RobotEvents;

public partial class RobotEventsBaseManager : DomainService
{
  private readonly HttpClient _httpClient = null;
  protected string _apiKey = string.Empty;
  protected Lazy<JsonSerializerSettings> _settings;

  public RobotEventsBaseManager()
  {
    _httpClient = new HttpClient();
    _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(RobotEventConsts.REScheme, RobotEventConsts.REKey);
    _settings = new Lazy<JsonSerializerSettings>(CreateSerializerSettings);
  }

  protected JsonSerializerSettings CreateSerializerSettings()
  {
    var settings = new JsonSerializerSettings();
    UpdateJsonSerializerSettings(settings);
    return settings;
  }

  protected JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }

  partial void UpdateJsonSerializerSettings(JsonSerializerSettings settings);


  public static RobotEventsBaseManager Create()
  {
    return new RobotEventsBaseManager();
  }

  protected virtual async Task<List<T>> GetItemsAsync<T>(string baseUrl, CancellationToken cancellationToken) where T : class, new()
  {
    List<T> list = new();
    var client = _httpClient;
    var disposeClient = false;

    try
    {
      var notDone = true;
      // configure url based on 
      var separator = baseUrl.Contains("?") ? "&" : "?";
      var url = $"{baseUrl}{separator}page=1";

      do
      {
        using (var request = new HttpRequestMessage())
        {
          request.Method = HttpMethod.Get;
          request.Headers.Accept.Add(MediaTypeWithQualityHeaderValue.Parse("application/json"));
          request.Headers.Add("Connection", "keep-alive");
          request.RequestUri = new Uri(url, UriKind.RelativeOrAbsolute);

          HttpResponseMessage response = await client.SendAsync(request, HttpCompletionOption.ResponseHeadersRead, cancellationToken).ConfigureAwait(false);
          var disposeResponse = true;
          try
          {
            var headers = Enumerable.ToDictionary(response.Headers, h => h.Key, h => h.Value);
            if (response.Content != null && response.Content.Headers != null)
            {
              foreach (var item in response.Content.Headers)
                headers[item.Key] = item.Value;
            }

            var status = (int)response.StatusCode;
            if (status == 200)
            {
                var objectResponse = await ReadObjectResponseAsync<PaginatedItem<T>>(response, headers).ConfigureAwait(false);
                if (objectResponse.Object == null)
                {
                  throw new ApiException("Response was null which was not expected.", status, objectResponse.Text, headers, null);
                }
                var pageList = objectResponse.Object.Data.ToList();
                list.AddRange(pageList);
                notDone = objectResponse.Object.Meta.Current_page < objectResponse.Object.Meta.Last_page;

                url = $"{baseUrl}{separator}page={objectResponse.Object.Meta.Current_page + 1}";
            }
            else if (status == 429)
            {
              // we exceeded the rate limit. sleep for a while
              await Task.Delay(10000);
            }
            else
            {
              var responseData = response.Content == null ? null : await response.Content.ReadAsStringAsync().ConfigureAwait(false);
              throw new ApiException($"The HTTP status code of the response was not expected ({status}).", status, responseData, headers, null);
            }
          }
          finally
          {
            if (disposeResponse)
              response.Dispose();
          }
        } // using
      } while (notDone);
    }
    finally
    {
      if (disposeClient)
        client.Dispose();
    }
    return await Task.FromResult(list);
  }

  protected struct ObjectResponseResult<T>
  {
    public ObjectResponseResult(T responseObject, string responseText)
    {
      this.Object = responseObject;
      this.Text = responseText;
    }

    public T Object { get; }

    public string Text { get; }
  }

  public bool ReadResponseAsString { get; set; }

  protected virtual async Task<ObjectResponseResult<T>> ReadObjectResponseAsync<T>(HttpResponseMessage response, IReadOnlyDictionary<string, IEnumerable<string>> headers)
  {
    if (response == null || response.Content == null)
    {
      return new ObjectResponseResult<T>(default(T), string.Empty);
    }

    if (ReadResponseAsString)
    {
      var responseText = await response.Content.ReadAsStringAsync().ConfigureAwait(false);
      try
      {
        var typedBody = JsonConvert.DeserializeObject<T>(responseText, JsonSerializerSettings);
        return new ObjectResponseResult<T>(typedBody, responseText);
      }
      catch (JsonException exception)
      {
        var message = "Could not deserialize the response body string as " + typeof(T).FullName + ".";
        throw new ApiException(message, (int)response.StatusCode, responseText, headers, exception);
      }
    }
    else
    {
      try
      {
        using (var responseStream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false))
        using (var streamReader = new StreamReader(responseStream))
        using (var jsonTextReader = new JsonTextReader(streamReader))
        {
          var serializer = JsonSerializer.Create(JsonSerializerSettings);
          var typedBody = serializer.Deserialize<T>(jsonTextReader);
          return new ObjectResponseResult<T>(typedBody, string.Empty);
        }
      }
      catch (JsonException exception)
      {
        var message = "Could not deserialize the response body stream as " + typeof(T).FullName + ".";
        throw new ApiException(message, (int)response.StatusCode, string.Empty, headers, exception);
      }
    }
  }


  protected virtual string ConvertToString(object value, CultureInfo cultureInfo)
  {
    if (value == null)
    {
      return "";
    }

    if (value is Enum)
    {
      var name = Enum.GetName(value.GetType(), value);
      if (name != null)
      {
        var field = IntrospectionExtensions.GetTypeInfo(value.GetType()).GetDeclaredField(name);
        if (field != null)
        {
          var attribute = CustomAttributeExtensions.GetCustomAttribute(field, typeof(EnumMemberAttribute))
              as EnumMemberAttribute;
          if (attribute != null)
          {
            return attribute.Value != null ? attribute.Value : name;
          }
        }

        var converted = Convert.ToString(Convert.ChangeType(value, Enum.GetUnderlyingType(value.GetType()), cultureInfo));
        return converted == null ? string.Empty : converted;
      }
    }
    else if (value is bool)
    {
      return Convert.ToString((bool)value, cultureInfo).ToLowerInvariant();
    }
    else if (value is byte[])
    {
      return Convert.ToBase64String((byte[])value);
    }
    else if (value.GetType().IsArray)
    {
      var array = Enumerable.OfType<object>((Array)value);
      return string.Join(",", Enumerable.Select(array, o => ConvertToString(o, cultureInfo)));
    }

    var result = Convert.ToString(value, cultureInfo);
    return result == null ? "" : result;
  }

}
