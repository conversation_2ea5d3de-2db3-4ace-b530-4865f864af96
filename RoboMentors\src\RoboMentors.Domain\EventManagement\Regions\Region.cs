﻿using System;
using Volo.Abp.Domain.Entities;

namespace RoboMentors.EventManagement.Regions;

public class Region : Entity<Guid>
{
  public virtual string Country { get; set; } = string.Empty;
  public virtual string RegionName { get; set; } = string.Empty;

	// Parameterless Constructor for ORMS
  protected Region()
  {
  }

  // Primary Constructor
  public Region(Guid id, string country, string regionName)
  {
    Id = id;
    Country = country;
    RegionName = regionName;
  }
}

