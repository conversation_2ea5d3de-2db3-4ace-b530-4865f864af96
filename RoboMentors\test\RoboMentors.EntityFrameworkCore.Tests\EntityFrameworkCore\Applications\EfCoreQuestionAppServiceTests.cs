using RoboMentors.Judging.Questions;
using Xunit;

namespace RoboMentors.EntityFrameworkCore.Applications;

[Collection(RoboMentorsTestConsts.CollectionDefinitionName)]
public class EfCoreQuestionAppServiceTests : QuestionAppService_Tests<RoboMentorsEntityFrameworkCoreTestModule>
{
    // This concrete class inherits all the test methods from QuestionAppService_Tests
    // and provides the specific TStartupModule (RoboMentorsEntityFrameworkCoreTestModule)
    // which includes Entity Framework Core setup with SQLite in-memory database
}
