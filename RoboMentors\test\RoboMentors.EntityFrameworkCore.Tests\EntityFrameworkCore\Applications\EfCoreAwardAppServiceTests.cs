using RoboMentors.Judging.Awards;
using Xunit;

namespace RoboMentors.EntityFrameworkCore.Applications;

[Collection(RoboMentorsTestConsts.CollectionDefinitionName)]
public class EfCoreAwardAppServiceTests : AwardAppService_Tests<RoboMentorsEntityFrameworkCoreTestModule>
{
    // This concrete class inherits all the test methods from AwardAppService_Tests
    // and provides the specific TStartupModule (RoboMentorsEntityFrameworkCoreTestModule)
    // which includes Entity Framework Core setup with SQLite in-memory database
}
