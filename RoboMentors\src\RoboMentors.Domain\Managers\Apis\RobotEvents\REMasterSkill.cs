﻿namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public class REMasterSkill
{
  [Newtonsoft.Json.JsonProperty("Season Name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SeasonName { get; set; }

  [Newtonsoft.Json.JsonProperty("Event Code", Required = Newtonsoft.Json.Required.Always)]
  public string EventSku { get; set; }

  [Newtonsoft.Json.JsonProperty("Event Start Date", Required = Newtonsoft.Json.Required.Always)]
  public string EventDate { get; set; }

  [Newtonsoft.Json.JsonProperty("Team", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string TeamNumber { get; set; }

  [Newtonsoft.Json.JsonProperty("Program", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Program { get; set; }

  [Newtonsoft.Json.JsonProperty("Grade Level", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string GradeLevel { get; set; }

  [Newtonsoft.Json.JsonProperty("Rank", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int Rank { get; set; }

  [Newtonsoft.Json.JsonProperty("Score", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int TotalScore { get; set; }

  [Newtonsoft.Json.JsonProperty("Prog Skills", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int ProgramScore { get; set; }

  [Newtonsoft.Json.JsonProperty("Driver Skills", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int DriverScore { get; set; }

  [Newtonsoft.Json.JsonProperty("Highest Prog", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int HighestProgram { get; set; }

  [Newtonsoft.Json.JsonProperty("Highest Driver", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int HighestDriver { get; set; }

  [Newtonsoft.Json.JsonProperty("Prog Stop Time", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int? ProgramStopTime { get; set; }

  [Newtonsoft.Json.JsonProperty("Driver Stop Time", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public int? DriverStopTime { get; set; }

  [Newtonsoft.Json.JsonProperty("Prog Scored At", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string ProgramScoredAt { get; set; }

  [Newtonsoft.Json.JsonProperty("Driver Scored At", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string DriverScoredAt { get; set; }

  [Newtonsoft.Json.JsonProperty("Team Name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string TeamName { get; set; }

  [Newtonsoft.Json.JsonProperty("Organization", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Organization { get; set; }

  [Newtonsoft.Json.JsonProperty("City", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string City { get; set; }

  [Newtonsoft.Json.JsonProperty("Region", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Region { get; set; }

  [Newtonsoft.Json.JsonProperty("Country", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Country { get; set; }
}
