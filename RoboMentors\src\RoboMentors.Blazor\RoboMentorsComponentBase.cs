﻿using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using RoboMentors.Localization;
using RoboMentors.Settings;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Components;
using Volo.Abp.Settings;

namespace RoboMentors.Blazor;

public abstract class RoboMentorsComponentBase : AbpComponentBase
{
  protected RoboMentorsComponentBase()
  {
    LocalizationResource = typeof(RoboMentorsResource);
  }

  // Services
  [Inject]
  public ISettingProvider SettingProvider { get; set; }

  //[Inject]
  //protected IEventSecurityAppService EventSecurityAppService { get; set; }

  [Inject]
  public NavigationManager UriHelper { get; set; }

  [Inject]
  IJSRuntime jsRunTime { get; set; }

  // Page Variables
  public string? RMEnvironment = string.Empty;
  public string? RMVersion = string.Empty;
  //protected MyEventDto MyEventDto = new();
  //public List<EventPlanningDivisionDto> ExtendedPlanningDivisionDtos = new();
  //public List<EventAwardLookupDto> ExtendedAwardLookupDtos = new();
  public int DefaultPlanningDivisionId = 0;
  public int SelectedPlanningDivisionId = 0;


  public async Task GetSettingsAsync()
  {
    RMEnvironment = await SettingProvider.GetOrNullAsync(RoboMentorsSettings.DeploymentEnvironment).ConfigureAwait(false);
    RMEnvironment = RMEnvironment?.ToLower() ?? string.Empty;
    RMVersion = await SettingProvider.GetOrNullAsync(RoboMentorsSettings.Version).ConfigureAwait(false);
    RMVersion = RMVersion?.ToLower() ?? string.Empty;
  }

  //protected async Task GetEventSecurityDtoAsync(int eventId, string teamType = "none")
  //{
  //  MyEventDto = await EventSecurityAppService.GetMyEventSecurityDtoAsync(eventId, teamType).ConfigureAwait(false);
  //  // Is User NOT Authorized for this event?
  //  if (MyEventDto.IsAuthorizedForEvent == false)
  //  {
  //    // Give NOT Authorized Message & return to Home page
  //    await Message.Error(L["Event:Unauthorized"]);
  //    UriHelper.NavigateTo("/");
  //  }
  //  // User Authorized for event, continue
  //  MyEventDto.Details.EventJudgingStatusColor = GetEventJudgingStatusColor(MyEventDto.Details.EventJudgingStatus);
  //}

  //protected async Task RefreshEventSecurityAsync(int eventId)
  //{
  //  await EventSecurityAppService.RemoveEventCacheDtoAsync(eventId).ConfigureAwait(false);
  //  await GetEventSecurityDtoAsync(eventId).ConfigureAwait(false);
  //}

  //protected void GetExtendedPlanningDivisionsAsync()
  //{
  //  ExtendedPlanningDivisionDtos = MyEventDto.Details.DivisionDtos.Planning;
  //  // if more than 1 JudgingDivision, then set default = Unassigned, else = 1 - 1st division;
  //  DefaultPlanningDivisionId = ExtendedPlanningDivisionDtos.Count > 1 ?
  //      (int)StandardPlanningDivision.Unassigned : ExtendedPlanningDivisionDtos[0].DivisionId;
  //  if (ExtendedPlanningDivisionDtos.Count > 1)
  //  {
  //    ExtendedPlanningDivisionDtos.Add(new EventPlanningDivisionDto(MyEventDto.Details.Id, -1, StandardPlanningDivision.Unavailable.ToString(), -1, 0));
  //    ExtendedPlanningDivisionDtos.Add(new EventPlanningDivisionDto(MyEventDto.Details.Id, 0, StandardPlanningDivision.Unassigned.ToString(), 0, 0));
  //    if (MyEventDto.Details.IsWorlds())
  //    {
  //      ExtendedPlanningDivisionDtos.Add(new EventPlanningDivisionDto(MyEventDto.Details.Id, (int)StandardPlanningDivision.RemoteJudges, StandardPlanningDivision.RemoteJudges.ToString(), (int)StandardPlanningDivision.RemoteJudges, 0));
  //      ExtendedPlanningDivisionDtos.Add(new EventPlanningDivisionDto(MyEventDto.Details.Id, (int)StandardPlanningDivision.Notebooks, StandardPlanningDivision.Notebooks.ToString(), (int)StandardPlanningDivision.Notebooks, 0));
  //    }
  //    ExtendedPlanningDivisionDtos = ExtendedPlanningDivisionDtos.OrderBy(x => x.Order).ToList();
  //  }
  //}

  //protected virtual string GetPlanningDivisionName(int planningDivisionId)
  //{
  //  return EventSecurityAppService.GetEventPlanningDivisionName(planningDivisionId, ExtendedPlanningDivisionDtos);
  //}

  //protected void GetExtendedAwardDtosAsync()
  //{
  //  ExtendedAwardLookupDtos = MyEventDto.Details.EventAwardLookupDtos;
  //  ExtendedAwardLookupDtos.Add(new EventAwardLookupDto { EventId = MyEventDto.Details.Id, Id = (int)JudgeTeam.Unavailable, Title = JudgeTeam.Unavailable.ToString(), AwardId = -1 });
  //  ExtendedAwardLookupDtos.Add(new EventAwardLookupDto { EventId = MyEventDto.Details.Id, Id = (int)JudgeTeam.Unassigned, Title = JudgeTeam.Unassigned.ToString(), AwardId = 0 });
  //  ExtendedAwardLookupDtos = ExtendedAwardLookupDtos.OrderBy(x => x.AwardId).ToList();
  //}


  //protected virtual string GetEventJudgingStatusColor(EventJudgingStatus status)
  //{
  //  switch (status)
  //  {
  //    case EventJudgingStatus.Planning:
  //      return "orange";
  //    case EventJudgingStatus.Active:
  //      return "green";
  //    case EventJudgingStatus.Disabled:
  //      return "red";
  //    case EventJudgingStatus.Closed:
  //      return "grey";
  //    default:
  //      return "red";
  //  }
  //}

  protected async Task SendMessageAndGoToEventHome(string message, int eventId)
  {
    await Message.Warn(L[message]);
    UriHelper.NavigateTo($"/event/{eventId}");
  }

  protected async Task ConsoleLogAsync(string message)
  {
    await jsRunTime.InvokeAsync<string>("console.log", message);
  }

}
