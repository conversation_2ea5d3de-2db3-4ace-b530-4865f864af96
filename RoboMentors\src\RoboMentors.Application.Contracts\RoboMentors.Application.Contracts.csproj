﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>RoboMentors</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\RoboMentors.Domain.Shared\RoboMentors.Domain.Shared.csproj" />
  </ItemGroup>
  
  <ItemGroup>
    <PackageReference Include="Volo.Abp.PermissionManagement.Application.Contracts" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application.Contracts" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application.Contracts" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Identity.Application.Contracts" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Account.Application.Contracts" Version="9.3.0" />
  </ItemGroup>
  
  <ItemGroup>
    <Folder Include="Identity\Users\" />
    <Folder Include="Identity\Roles\" />
  </ItemGroup>

</Project>
