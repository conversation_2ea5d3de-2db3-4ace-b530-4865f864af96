﻿namespace RoboMentors.EventManagement.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public enum EventLevel
{
  [System.Runtime.Serialization.EnumMember(Value = @"World")]
  World = 0,

  [System.Runtime.Serialization.EnumMember(Value = @"National")]
  National = 1,

  [System.Runtime.Serialization.EnumMember(Value = @"Regional")]
  Regional = 2,

  [System.Runtime.Serialization.EnumMember(Value = @"State")]
  State = 3,

  [System.Runtime.Serialization.EnumMember(Value = @"Signature")]
  Signature = 4,

  [System.Runtime.Serialization.EnumMember(Value = @"Other")]
  Other = 5,
}
