﻿namespace RoboMentors;

public static class RoboMentorsSharedConsts
{
  // Length type constants
  public const int String0128 = 128;
  public const int String0256 = 256;
  public const int String0512 = 512;
  public const int String1024 = 1024;
  public const int String2048 = 2048;
  public const int String4096 = 4096;
  public const int UrlMaxLength = String2048;

  // Constants
  public const int PageSizeDefault = 10;
  // Use this value when not using @bind-Text for MemoEdit & Debounce=false
  public const int MemoEditDebounceInterval = 1500; // 1.5 sec * 1000 ms/sec = 1500 ms
}
