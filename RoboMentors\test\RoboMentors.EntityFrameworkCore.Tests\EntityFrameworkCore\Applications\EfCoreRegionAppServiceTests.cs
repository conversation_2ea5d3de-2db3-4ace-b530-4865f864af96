﻿using RoboMentors.EventManagement.Regions;
using Xunit;

namespace RoboMentors.EntityFrameworkCore.Applications;

[Collection(RoboMentorsTestConsts.CollectionDefinitionName)]
public class EfCoreRegionAppServiceTests : RegionAppService_Tests<RoboMentorsEntityFrameworkCoreTestModule>
{
  // This concrete class inherits all the test methods from RegionAppService_Tests
  // and provides the specific TStartupModule (RoboMentorsEntityFrameworkCoreTestModule)
  // which includes Entity Framework Core setup with SQLite in-memory database
}
