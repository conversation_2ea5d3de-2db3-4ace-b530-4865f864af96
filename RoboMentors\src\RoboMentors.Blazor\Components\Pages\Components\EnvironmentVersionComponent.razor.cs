﻿using Microsoft.AspNetCore.Components;
using RoboMentors.Settings;
using System;
using System.Threading.Tasks;
using Volo.Abp.Settings;

namespace RoboMentors.Blazor.Components.Pages.Components;

public partial class EnvironmentVersionComponent : RoboMentorsComponentBase
{
  // Services

  // Page Variables
  private string Color = "bg-danger";

  protected override async Task OnInitializedAsync()
  {
    await GetSettingsAsync().ConfigureAwait(false);
    if (RMEnvironment?.Equals("local") == true) { Color = "bg-warning"; }
    else if (RMEnvironment?.Equals("development") == true) { Color = "bg-secondary"; }
    else if (RMEnvironment?.Equals("qa") == true) { Color = "bg-primary"; }
    else if (RMEnvironment?.Equals("production") == true) { Color = ""; }
  }
}
