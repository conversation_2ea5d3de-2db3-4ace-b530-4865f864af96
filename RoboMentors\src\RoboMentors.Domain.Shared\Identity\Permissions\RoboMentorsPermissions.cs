﻿namespace RoboMentors.Identity.Permissions;

public static class RoboMentorsPermissions
{
  public const string GroupName = "RoboMentors";
  public const string EventManagementGroupName = "EventManagement";
  public const string VolunteerManagementGroupName = "VolunteerManagement";
  public const string JudgingGroupName = "Judging";
  public const string ReffingGroupName = "Reffing";
  public const string AutomationGroupName = "Automation";
  public const string TrainingGroupName = "Training";

  // -----
  // Event Management Permissions
  // -----
  //* Events
  public static class Events
  {
    public const string Default = EventManagementGroupName + ".Events";
    public const string Create = Default + ".Create";
    public const string Edit = Default + ".Edit";
    public const string Delete = Default + ".Delete";
    public const string Users = Default + ".Users";
    public const string Interview = Default + ".Interview";
  }

  //* Regions
  public static class Regions
  {
    public const string Default = EventManagementGroupName + ".Regions";
    public const string Create = Default + ".Create";
    public const string Edit = Default + ".Edit";
    public const string Delete = Default + ".Delete";
    public const string Synchronize = Default + ".Synchronize";
  }

  public static class Teams
  {
    public const string Default = JudgingGroupName + ".Teams";
    public const string Synchronize = Default + ".Synchronize";
  }

  // -----
  // Judging Permissions
  // -----
  //* Awards
  public static class Awards
  {
    public const string Default = JudgingGroupName + ".Awards";
    public const string Create = Default + ".Create";
    public const string Edit = Default + ".Edit";
    public const string Delete = Default + ".Delete";
  }
  //* Questions
  public static class Questions
  {
    public const string Default = JudgingGroupName + ".Questions";
    public const string Create = Default + ".Create";
    public const string Edit = Default + ".Edit";
    public const string Delete = Default + ".Delete";
  }

  // -----
  // Automation Permissions
  // -----
  public static class Services
  {
    public const string Default = AutomationGroupName + ".Services";
    public const string Execute = Default + ".Execute";
  }

}
