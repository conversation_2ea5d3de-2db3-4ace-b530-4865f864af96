﻿using Blazorise;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Components;
using RoboMentors.Judging.Awards;
using RoboMentors.Judging.Interviews;
using RoboMentors.Judging.Questions;
using RoboMentors.Identity.Permissions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.AspNetCore.Components.Web.Theming.PageToolbars;

namespace RoboMentors.Blazor.Components.Pages.Judging.Questions;

public partial class Questions : RoboMentorsComponentBase
{
  // Service Injections
  [Inject]
  public IQuestionAppService QuestionAppService { get; set; }

  [Inject]
  public IAwardAppService AwardAppService { get; set; }

  // Page Variables
  protected PageToolbar Toolbar { get; } = new();
  private const string All = "All";


  private List<QuestionDto> QuestionDtos = new();
  private List<QuestionDto> FilteredQuestionDtos = new();
  private CreateUpdateQuestionDto CUQuestionDto = new();
  private List<AwardKeywordLookupDto> AwardKeywordLookupDtos = new();

  private string FilterInterviewType = string.Empty;
  private string FilterAwardKeyword = string.Empty;
  private string FilterText = string.Empty;

  private int PageSize = RoboMentorsSharedConsts.PageSizeDefault;

  protected bool CanCreate = false;
  protected bool CanEdit = false;
  protected bool CanDelete = false;
  protected bool IsReadOnly = true;

  private Modal CreateUpdateModal = new();
  private FormMode FormMode = FormMode.Unknown;

  private Validations ValidationsRef = new();

  private List<Volo.Abp.BlazoriseUI.BreadcrumbItem> BreadcrumbItems = new();

  protected override async Task OnInitializedAsync()
  {
    PageSetup();
    await SetPermissionsAsync().ConfigureAwait(false);
    await GetAwardLookupDtos().ConfigureAwait(false);
    await GetDtosAsync().ConfigureAwait(false);
  }
  protected void PageSetup()
  {
    Toolbar.AddButton(L["Question:New"],
        () => { OpenModalForNew(); return Task.CompletedTask; },
        icon: IconName.Add,
        color: Color.Primary,
        requiredPolicyName: RoboMentorsPermissions.Questions.Create
    );

    BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Maintenance"]));
    BreadcrumbItems.Add(new Volo.Abp.BlazoriseUI.BreadcrumbItem(L["Questions"]));
  }

  private async Task SetPermissionsAsync()
  {
    CanCreate = await AuthorizationService.IsGrantedAsync(RoboMentorsPermissions.Questions.Create);
    CanEdit = await AuthorizationService.IsGrantedAsync(RoboMentorsPermissions.Questions.Edit);
    CanDelete = await AuthorizationService.IsGrantedAsync(RoboMentorsPermissions.Questions.Delete);
  }

  private async Task GetAwardLookupDtos()
  {
    AwardKeywordLookupDtos = await AwardAppService.GetKeywordLookupAsync().ConfigureAwait(false);
    AwardKeywordLookupDtos.Add(new AwardKeywordLookupDto() { Id = 99, Keyword = All, Order = 99 });
  }

  private async Task GetDtosAsync()
  {
    QuestionDtos = await QuestionAppService.GetDtosAsync().ConfigureAwait(false);
    UpdateFilter();
  }

  private void UpdateFilterInterviewType(string filter)
  {
    FilterInterviewType = filter;
    UpdateFilter();
  }
  private void UpdateFilterAwardKeyword(string filter)
  {
    FilterAwardKeyword = filter;
    UpdateFilter();
  }
  private void UpdateFilterText(string filter)
  {
    FilterText = filter;
    UpdateFilter();
  }

  private void ClearFilterText()
  {
    FilterText = string.Empty;
    UpdateFilter();
  }

  private void ClearFilters()
  {
    FilterInterviewType = string.Empty;
    FilterAwardKeyword = string.Empty;
    FilterText = string.Empty;
    UpdateFilter();
  }

  private void UpdateFilter()
  {
    var searchWords = FilterText.IsNullOrEmpty() ? new List<string>() : FilterText.Split(' ').ToList();
    FilteredQuestionDtos = QuestionDtos.Where(question =>
        (string.IsNullOrEmpty(FilterInterviewType) ||
            question.InterviewTypeText.ToLower() == FilterInterviewType.ToLower()) &&
        (string.IsNullOrEmpty(FilterAwardKeyword) ||
            question.AwardKeyword.ToLower() == FilterAwardKeyword.ToLower()) &&
        (string.IsNullOrEmpty(FilterText) ||
            searchWords.All(word => question.Text.ToLower().Contains(word.ToLower())))
    ).ToList();
  }

  private async Task OpenModalForNew()
  {
    await ValidationsRef.ClearAll();
    CUQuestionDto = new()
    {
      InterviewType = InterviewType.Initial,
      AwardId = 99
    };
    FormMode = FormMode.New;
    IsReadOnly = CanCreate == false;
    await CreateUpdateModal.Show();
  }

  private async Task OpenModalForEdit(QuestionDto question)
  {
    await ValidationsRef.ClearAll();
    CUQuestionDto = ObjectMapper.Map<QuestionDto, CreateUpdateQuestionDto>(question);
    FormMode = FormMode.Edit;
    IsReadOnly = CanEdit == false;
    await CreateUpdateModal.Show();
  }

  private async Task CloseModal()
  {
    await CreateUpdateModal.Hide();
  }

  private async Task SaveAsync()
  {
    if (await ValidationsRef.ValidateAll() == false)
    {
      return;
    }
    var result = new ApiResult<QuestionDto>();
    if (FormMode == FormMode.New)
    {
      result = await QuestionAppService.CreateAsync(CUQuestionDto).ConfigureAwait(false);
    }
    else
    {
      result = await QuestionAppService.UpdateAsync(CUQuestionDto).ConfigureAwait(false);
    }
    if (result.Success)
    {
      await CreateUpdateModal.Hide();
      await GetDtosAsync().ConfigureAwait(false);
    }
    else
    {
      var message = L[result.ErrorMessage, result.ErrorMessageParam];
      await Message.Warn(message, "Errors:Questions");
    }
  }

  private async Task DeleteAsync()
  {
    if (await Message.Confirm(L["AreYouSureToDelete", $"Question for {CUQuestionDto.InterviewType} Interviews, Question:  {CUQuestionDto.Text}"], L["AreYouSure"]) == false)
    {
      return;
    }
    await QuestionAppService.DeleteAsync(CUQuestionDto.Id).ConfigureAwait(false);
    await GetDtosAsync().ConfigureAwait(false);
    await CreateUpdateModal.Hide();
  }
}
