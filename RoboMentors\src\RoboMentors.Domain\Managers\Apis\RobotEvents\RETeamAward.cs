﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public class RETeamAward
{
  [Newtonsoft.Json.JsonProperty("season", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SeasonName { get; set; }

  [Newtonsoft.Json.JsonProperty("event_code", Required = Newtonsoft.Json.Required.Always)]
  public string EventSku { get; set; }

  [Newtonsoft.Json.JsonProperty("event_date", Required = Newtonsoft.Json.Required.Always)]
  public string EventDate { get; set; }

  [Newtonsoft.Json.JsonProperty("event_name", Required = Newtonsoft.Json.Required.Always)]
  public string EventName { get; set; }

  [Newtonsoft.Json.JsonProperty("level_class", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string LevelClass { get; set; }

  [Newtonsoft.Json.JsonProperty("team", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string TeamNumber { get; set; }

  [Newtonsoft.Json.JsonProperty("team_name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string TeamName { get; set; }

  [Newtonsoft.Json.JsonProperty("award_name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string AwardTitle { get; set; }

  [Newtonsoft.Json.JsonProperty("program", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Program { get; set; }

  [Newtonsoft.Json.JsonProperty("grade_level", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string GradeLevel { get; set; }

  [Newtonsoft.Json.JsonProperty("city", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string City { get; set; }

  [Newtonsoft.Json.JsonProperty("region", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Region { get; set; }

  [Newtonsoft.Json.JsonProperty("country", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Country { get; set; }
}
