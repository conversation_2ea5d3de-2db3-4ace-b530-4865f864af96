﻿using Microsoft.EntityFrameworkCore;
using RoboMentors.EntityFrameworkCore;
using RoboMentors.EventManagement.RobotEvents;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using RoboMentors.Extensions;

namespace RoboMentors.EventManagement.Teams
{
  public class EfCoreTeamRepository :
      EfCoreRepository<RoboMentorsDbContext, Team>,
      ITeamRepository
  {
    public EfCoreTeamRepository(
        IDbContextProvider<RoboMentorsDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<List<Team>> GetTeamsThatStartWithAsync(string? numberStartsWith, int limit = 200)
    {
      var dbSet = await GetDbSetAsync().ConfigureAwait(false);
      var query = dbSet.AsQueryable<Team>();
      if (!string.IsNullOrEmpty(numberStartsWith))
      {
          query = query.Where(x => x.Number.StartsWith(numberStartsWith));
      }
      return await query.OrderBy(x=>x.Number).Take(limit).ToListAsync<Team>().ConfigureAwait(false);
    }

    public async Task<Dictionary<string, string>> GetProgramTeamNumberRegionsAsync(EventProgram? program = null)
    {
      var dbSet = await GetDbSetAsync().ConfigureAwait(false);
      var query = dbSet.AsQueryable<Team>();
      return await query
          .WhereIf(program != null, x => x.Program == program)
          .OrderBy(x => x.Number.Length)
          .ThenBy(x => x.Number)
          .ThenBy(x => x.Program)
          .Select(x => new { Key = x.Program.ToProgramTeamNumber(x.Number, TeamConsts.ProgramTeamNumberSeparator), Value = x.AffiliateRegion } )
          .ToDictionaryAsync(x => x.Key, x=> x.Value)
          .ConfigureAwait(false);
    }

		public async Task<List<Team>> FindByProgramAsync(EventProgram program)
		{
			var dbSet = await GetDbSetAsync().ConfigureAwait(false);
			return await dbSet
					.Where(x => x.Program == program)
					.ToListAsync<Team>().ConfigureAwait(false);
		}

    public async Task<Team?> FindByProgramTeamNumberAsync(EventProgram program, string teamNumber)
    {
      var dbSet = await GetDbSetAsync().ConfigureAwait(false);
      return await dbSet
          .Where(x => x.Program == program)
          .Where(x => x.Number == teamNumber)
          .FirstOrDefaultAsync<Team>().ConfigureAwait(false);
    }
	}
}
