﻿using System.Collections.Generic;
using System.Threading.Tasks;

namespace RoboMentors.Managers.Apis.RobotEvents;

public interface IRobotEventsWCManager
{
  Task<List<REJudgingSchedule>> GetREJudgingSchedulesAsync(string environment);
  Task<List<REWorldsDen>> GetREWorldsDenAsync();
  Task<List<REWorldsDen>> GetREEventDensAsync(string sku);
  Task<List<RETeamAward>> GetRETeamAwardsAsync(string program);
  Task<List<REMasterSkill>> GetREMasterSkillsAsync(string program);
}
