﻿@page "/maintenance/awards"
@attribute [Authorize(RoboMentorsPermissions.Awards.Default)]
@using RoboMentors.EventManagement.EventAwards;
@using RoboMentors.EventManagement.RobotEvents;
@using RoboMentors.Judging.Awards;
@using RoboMentors.Localization;
@using RoboMentors.Identity.Permissions;
@using System.ComponentModel.DataAnnotations;
@using Volo.Abp.AspNetCore.Components.Web;
@using Volo.Abp.AspNetCore.Components.Web.Theming.Layout
@inherits RoboMentorsComponentBase
@inject AbpBlazorMessageLocalizerHelper<RoboMentorsResource> LH

<PageHeader Title="@L["Menu:Maintenance:Judging:Awards"]" Toolbar="@Toolbar" BreadcrumbItems="@BreadcrumbItems"></PageHeader>
<Card>
	<CardBody>
		<DataGrid TItem="AwardDto" Data="AwardDtos" TotalItems="AwardDtos.Count"
							ShowPager="@( AwardDtos.Count > PageSize)" PagerPosition="DataGridPagerPosition.Bottom"
							ShowPageSizes="@( AwardDtos.Count > PageSize)" PageSize="PageSize"
							Striped="false" Bordered="false" Hoverable="true" Responsive="true"
							SelectedRowChanged="@((row) => { OpenModalForEdit(row); } )">
			<LoadingTemplate><RadarSpinner></RadarSpinner></LoadingTemplate>
			<DataGridColumns>
				<DataGridNumericColumn TItem="AwardDto" Field="@nameof(Award.Id)" Caption="@L["Award:Id"]" />
				<DataGridColumn TItem="AwardDto" Field="@nameof(Award.Keyword)" Caption="@L["Award:Keyword"]" />
				<DataGridColumn TItem="AwardDto" Field="@nameof(Award.Abbrev)" Caption="@L["Award:Abbrev"]" />
				<DataGridNumericColumn TItem="AwardDto" Field="@nameof(AwardDto.DefaultOrder)" Caption="@L["Award:DefaultOrder"]" />
				<DataGridNumericColumn TItem="AwardDto" Field="@nameof(AwardDto.VIQOrder)" Caption="@L["Award:VIQOrder"]" />
				<DataGridColumn TItem="AwardDto" Field="@nameof(AwardDto.Title)" Caption="@L["Award:Title"]" />
				<DataGridColumn TItem="AwardDto" Field="@nameof(AwardDto.Program)" Caption="@L["Award:Program"]">
					<DisplayTemplate>@L[$"Enum:EventProgram:{(int)context.Program}"]</DisplayTemplate>
				</DataGridColumn>
				<DataGridColumn TItem="AwardDto" Field="@nameof(AwardDto.Type)" Caption="@L["Award:Type"]">
					<DisplayTemplate>@L[$"Enum:AwardType:{(int)context.Type}"]</DisplayTemplate>
				</DataGridColumn>
				<DataGridCheckColumn TItem="AwardDto" Field="@nameof(AwardDto.IncludeOnInitialRanking)" Caption="@L["Award:IncludeOnInitialRanking"]" />
				<DataGridCheckColumn TItem="AwardDto" Field="@nameof(AwardDto.IncludeOnFollowUpRanking)" Caption="@L["Award:IncludeOnFollowUpRanking"]" />
				<DataGridNumericColumn TItem="AwardDto" Field="@nameof(AwardDto.FollowUpInterviewDefaultMinutes)" Caption="@L["Award:FollowUpInterviewDefaultMinutes"]" />
			</DataGridColumns>
		</DataGrid>
	</CardBody>
</Card>

@* *** Create/Edit Modal *@
<Modal @ref="CreateUpdateModal" Animated="true">
	<ModalContent Size="ModalSize.Large" Centered="false">
		<Form>
			<ModalHeader>
				<ModalTitle>@L[$"Award:{FormMode.ToString()}"]</ModalTitle>
				<CloseButton Clicked="CloseModal" />
			</ModalHeader>
			<ModalBody>
				<Validations @ref="@ValidationsRef" Mode="ValidationMode.Auto" Model="@CUAwardDto" ValidateOnLoad="false">
					<Fields>
						<Field ColumnSize="ColumnSize.Is2">
							<FieldLabel>@L["Award:Id"]</FieldLabel>
							<NumericEdit @bind-Value="@CUAwardDto.Id" Disabled="true"></NumericEdit>
						</Field>
						<Validation Validator="@ValidateUniqueKeyword" MessageLocalizer="@LH.Localize">
							<Field ColumnSize="ColumnSize.Is4">
								<FieldLabel>@L["Award:Keyword"]</FieldLabel>
								<FieldBody>
									<TextEdit @bind-Text="@CUAwardDto.Keyword" Autofocus>
										<Feedback>
											<ValidationError></ValidationError>
										</Feedback>
									</TextEdit>
								</FieldBody>
							</Field>
						</Validation>
						<Validation MessageLocalizer="@LH.Localize">
							<Field ColumnSize="ColumnSize.Is2">
								<FieldLabel>@L["Award:Abbrev"]</FieldLabel>
								<FieldBody>
									<TextEdit @bind-Text="@CUAwardDto.Abbrev">
										<Feedback><ValidationError /></Feedback>
									</TextEdit>
								</FieldBody>
							</Field>
						</Validation>
						<Validation MessageLocalizer="@LH.Localize">
							<Field ColumnSize="ColumnSize.Is2">
								<FieldLabel>@L["Award:DefaultOrder"]</FieldLabel>
								<NumericEdit TValue="int" @bind-Value="@CUAwardDto.DefaultOrder" TextAlignment="TextAlignment.End" Min="AwardConsts.OrderMin" Max="AwardConsts.OrderMax">
									<Feedback><ValidationError>@L["Errors:Awards:Order", AwardConsts.OrderMin, AwardConsts.OrderMax]</ValidationError></Feedback>
								</NumericEdit>
							</Field>
						</Validation>
						<Validation MessageLocalizer="@LH.Localize">
							<Field ColumnSize="ColumnSize.Is2">
								<FieldLabel>@L["Award:VIQOrder"]</FieldLabel>
								<NumericEdit TValue="int" @bind-Value="@CUAwardDto.VIQOrder" TextAlignment="TextAlignment.End" Min="AwardConsts.OrderMin" Max="AwardConsts.OrderMax">
									<Feedback><ValidationError>@L["Errors:Awards:Order", AwardConsts.OrderMin, AwardConsts.OrderMax]</ValidationError></Feedback>
								</NumericEdit>
							</Field>
						</Validation>
					</Fields>
					<Validation MessageLocalizer="@LH.Localize">
						<Field>
							<FieldLabel>@L["Award:Title"]</FieldLabel>
							<FieldBody>
								<TextEdit @bind-Text="@CUAwardDto.Title">
									<Feedback><ValidationError /></Feedback>
								</TextEdit>
							</FieldBody>
						</Field>
					</Validation>
					<Fields>
						<Field>
							<FieldLabel>@L["Award:Program"]"</FieldLabel>
							<Select TValue="EventProgram" @bind-SelectedValue="@CUAwardDto.Program">
								@foreach (int eventProgramValue in Enum.GetValues(typeof(EventProgram)))
								{
									<SelectItem TValue="EventProgram" Value="@((EventProgram)eventProgramValue)">
										@L[$"Enum:EventProgram:{eventProgramValue}"]
									</SelectItem>
								}
							</Select>
						</Field>
						<Field>
							<FieldLabel>@L["Award:Type"]</FieldLabel>
							<Select TValue="AwardType" @bind-SelectedValue="@CUAwardDto.Type">
								@foreach (int awardTypeValue in Enum.GetValues(typeof(AwardType)))
								{
									<SelectItem TValue="AwardType" Value="@((AwardType)awardTypeValue)">
										@L[$"Enum:AwardType:{awardTypeValue}"]
									</SelectItem>
								}
							</Select>
						</Field>
					</Fields>
					<Validation Validator="ValidationRule.IsNotEmpty" MessageLocalizer="@LH.Localize">
						<Field>
							<FieldLabel>@L["Award:Description"]</FieldLabel>
							<MemoEdit @bind-Text="@CUAwardDto.Description" Rows="4">
								<Feedback><ValidationError>@L["Errors:Awards:Description"]</ValidationError></Feedback>
							</MemoEdit>
						</Field>
					</Validation>
					<Validation Validator="ValidationRule.IsNotEmpty" MessageLocalizer="@LH.Localize">
						<Field>
							<FieldLabel>@L["Award:Script"]</FieldLabel>
							<MemoEdit @bind-Text="@CUAwardDto.Script" Rows="5">
								<Feedback><ValidationError>@L["Errors:Awards:Script"]</ValidationError></Feedback>
							</MemoEdit>
						</Field>
					</Validation>
					<Validation Validator="ValidationRule.IsNotEmpty" MessageLocalizer="@LH.Localize">
						<Field>
							<FieldLabel>@L["Award:IconUrl"]</FieldLabel>
							<TextEdit @bind-Text="@CUAwardDto.Icon">
								<Feedback><ValidationError>@L["Errors:Awards:IconUrl"]</ValidationError></Feedback>
							</TextEdit>
						</Field>
					</Validation>
					<Field ColumnSize="ColumnSize.Is6">
						<FieldLabel>@L["Award:IncludeOnInitialRanking"]</FieldLabel>
						<Check @bind-Checked="@CUAwardDto.IncludeOnInitialRanking"></Check>
					</Field>
					<Fields>
						<Field ColumnSize="ColumnSize.Is6">
							<FieldLabel>@L["Award:IncludeOnFollowUpRanking"]</FieldLabel>
							<Check @bind-Checked="@CUAwardDto.IncludeOnFollowUpRanking"></Check>
						</Field>
						<Validation MessageLocalizer="@LH.Localize">
							<Field ColumnSize="ColumnSize.Is3">
								<FieldLabel>@L["Award:FollowUpInterviewDefaultMinutes"]</FieldLabel>
								<NumericEdit TValue="int" @bind-Value="@CUAwardDto.FollowUpInterviewDefaultMinutes" TextAlignment="TextAlignment.End" Min="AwardConsts.FollowUpInterviewMinutesMin" Max="AwardConsts.FollowUpInterviewMinutesMax">
									<Feedback><ValidationError>@L["Errors:Awards:FollowUpInterviewDefaultMinutes", AwardConsts.FollowUpInterviewMinutesMin, AwardConsts.FollowUpInterviewMinutesMax]</ValidationError></Feedback>
								</NumericEdit>
							</Field>
						</Validation>
					</Fields>
				</Validations>
			</ModalBody>
			<ModalFooter>
				@if (CanDelete && (FormMode == FormMode.Edit || FormMode == FormMode.Delete))
				{
					<Button Color="Color.Secondary" Clicked="@(async () => { await DeleteAsync().ConfigureAwait(false); })">@L["Delete"]</Button>
				}
				<Button Color="Color.Secondary" Clicked="CloseModal">@L["Cancel"]</Button>
				@if (IsReadOnly == false && (CanCreate || CanEdit))
				{
					<Button Color="Color.Primary" Clicked="@(async () => { await SaveAsync().ConfigureAwait(false); })">@L["Save"]</Button>
				}
			</ModalFooter>
		</Form>
	</ModalContent>
</Modal>
