﻿using RoboMentors.EventManagement.RobotEvents;
using System;
using Volo.Abp.Application.Dtos;

namespace RoboMentors.EventManagement.Teams;

public class TeamDto : EntityDto
{
  public int Id { get; set; }

  public string Number { get; set; }

  public string Name { get; set; }

  public string RobotName { get; set; }

  public string Organization { get; set; }

  public string City { get; set; }

  public string Region { get; set; }

  public string Country { get; set; }

  public Grade Grade { get; set; }

  public EventProgram Program { get; set; }

  public string ProgramText { get; set; }

  public double Latitude { get; set; }

  public double Longitude { get; set; }

  public string AffiliateRegion => String.IsNullOrWhiteSpace(Region) == false ? Region : Country;

  public TeamDto()
  {

  }

  public TeamDto(
          int id,
          string number,
          string name,
          string robotName,
          string organization,
          string city,
          string region,
          string country,
          Grade grade,
          EventProgram program,
          string programText,
          double latitude,
          double longitude)
  {
    Id = id;
    Number = number;
    Name = name;
    RobotName = robotName;
    Organization = organization;
    City = city;
    Region = region;
    Country = country;
    Grade = grade;
    Program = program;
    ProgramText = programText;
    Latitude = latitude;
    Longitude = longitude;
  }
}
