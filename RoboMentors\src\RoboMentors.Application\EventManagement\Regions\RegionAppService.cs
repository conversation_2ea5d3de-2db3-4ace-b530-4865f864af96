﻿using Microsoft.AspNetCore.Authorization;
using RoboMentors.EventManagement.RobotEvents;
using RoboMentors.Identity.Permissions;
using RoboMentors.Managers.Apis.RobotEvents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RoboMentors.EventManagement.Regions;

[Authorize(RoboMentorsPermissions.Regions.Default)]
public class RegionAppService : RoboMentorsAppService, IRegionAppService
{
  // Repositories
  private readonly IRegionRepository _regionRepository;
  private readonly IRobotEventsEventManager _robotEventsEventManager;

  // Services

  public RegionAppService(
      IRegionRepository regionRepository,
      IRobotEventsEventManager robotEventsEventManager)
  {
    _regionRepository = regionRepository;
    _robotEventsEventManager = robotEventsEventManager;
  }

  [Authorize(RoboMentorsPermissions.Regions.Synchronize)]
  public async Task<ApiResult<RegionDto>> SyncRegionsAsync(DateTime? startDate = null)
  {
    var results = new ApiResult<RegionDto>();
    var regionsAdded = 0;
    // get the regions
    var start = startDate ?? DateTime.Now.AddYears(-2);
    var eventRegions = await GetEventRegionsAsnyc(start).ConfigureAwait(false);

    // get the regions from the database    
    var regions = await _regionRepository.GetListAsync().ConfigureAwait(false);

    // loop through the event regions
    foreach (var eventRegion in eventRegions)
    {
      // try to get the region from the database
      var region = regions.FirstOrDefault(x => x.Country == eventRegion.Country && x.RegionName == eventRegion.Region);

      // does it exist?
      if (region == null)
      {
        // no, create it
        region = new Region(
          id: GuidGenerator.Create(),
          country: eventRegion.Country,
          regionName: eventRegion.Region
        );
        // add it to the database
        await _regionRepository.InsertAsync(region).ConfigureAwait(false);
        regionsAdded++;
      }
    }
    results.Success = true;
    results.Log.Add($"Successfully added {regionsAdded} regions based on {eventRegions} events that started after {start}.");
    return results;
  }

  public async Task<List<RegionDto>> GetRegionsAsync()
  {
    // get the regions from the database    
    var regions = await _regionRepository.GetListAsync().ConfigureAwait(false);
    return ObjectMapper.Map<List<Region>, List<RegionDto>>(regions);
  }

  protected virtual async Task<List<EventRegion>> GetEventRegionsAsnyc(DateTime? startDate = null)
  {
    var start = startDate ?? DateTime.Now.AddYears(-2);

    // get the events 
    var events = await _robotEventsEventManager.GetEventsAsync(start).ConfigureAwait(false);

    // get the unique event regions
    var eventRegions = events.Where(x => x.Location.Country.Equals(x.Location.Region) == false).Select(x => new EventRegion(x.Location.Country ?? string.Empty, x.Location.Region ?? string.Empty)).Distinct(new EventRegionComparer()).OrderBy(x => x.Country).ThenBy(x => x.Region).ToList();

    // get the countries that have regions
    var countries = eventRegions.Where(x => x.Region.Trim().Length > 0).GroupBy(x => x.Country).Where(x => x.Count() > 0).Select(x => x.Key.ToLower()).ToList();

    // filter out the countries that have an empty region
    return eventRegions.Where(x => IsRegionEmpty(x, countries) == false).ToList();
  }

  private bool IsRegionEmpty(EventRegion eventRegion, List<string> countries)
  {
    return countries.Contains(eventRegion.Country.ToLower()) == true && eventRegion.Region.Trim().Length == 0;
  }
}

internal class EventRegionComparer : IEqualityComparer<EventRegion>
{
  public bool Equals(EventRegion? x, EventRegion? y)
  {
    // Check for null values
    if (x == null || y == null)
      return false;

    // Check if the two Product objects are the same reference
    var xItem = $"{x.Country.Trim().ToLower()}.{x.Region.Trim().ToLower()}";
    var yItem = $"{y.Country.Trim().ToLower()}.{y.Region.Trim().ToLower()}";
    var result = xItem == yItem;
    return result;
  }

  public int GetHashCode(EventRegion? obj)
  {
    if (obj == null) return 0;
    return HashCode.Combine(obj.Country, obj.Region);
  }
}
