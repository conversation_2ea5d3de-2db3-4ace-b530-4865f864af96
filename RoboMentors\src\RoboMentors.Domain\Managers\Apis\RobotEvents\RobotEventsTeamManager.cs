﻿using Newtonsoft.Json;
using RoboMentors.EventManagement.RobotEvents;
using RoboMentors.EventManagement.Teams;
using RoboMentors.Judging.Awards;
using System;
using System.Buffers.Text;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace RoboMentors.Managers.Apis.RobotEvents;

public partial class RobotEventsTeamManager : RobotEventsBaseManager, IRobotEventsTeamManager
{
  private readonly string _baseUrl;

  public RobotEventsTeamManager() : base()
  {
    _baseUrl = RobotEventConsts.REBaseUrl;
  }

  protected JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }

  public static RobotEventsTeamManager Create()
  {
    return new RobotEventsTeamManager();
  }

  /// <param name="registered">Filter by whether or not the Team is Registered</param>
  /// <param name="program">Filter by Team Number</param>
  /// <returns>List of Teams</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<RETeam>> GetTeamsAsync(bool? registered, IEnumerable<int> program)
  {
    return await GetTeamsAsync(null, null, null, registered, program, null, null, null).ConfigureAwait(false);
  }
  /// <param name="number">Filter by Team Number</param>
  /// <returns>List of Teams</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<RETeam>> GetTeamsAsync(IEnumerable<string> number, IEnumerable<int> program)
  {
    return await GetTeamsAsync(null, number, null, null, program, null, null, null).ConfigureAwait(false);
  }
  /// <param name="id">Filter by Team ID</param>
  /// <param name="number">Filter by Team Number</param>
  /// <param name="@event">Filter by Events that Teams have attended</param>
  /// <param name="registered">Filter by whether or not the Team is Registered</param>
  /// <param name="program">Filter by the Program that the Team is part of</param>
  /// <param name="grade">Filter by the Grade of the Team</param>
  /// <param name="country">Filter by the Country of the Team</param>
  /// <param name="myTeams">Only show teams associated with the authenticated user.</param>
  /// <returns>List of Teams</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<RETeam>> GetTeamsAsync(IEnumerable<int> id, IEnumerable<string> number, IEnumerable<int> @event, bool? registered, IEnumerable<int> program, IEnumerable<Grade> grade, IEnumerable<string> country, bool? myTeams)
  {
    return await GetTeamsAsync(id, number, @event, registered, program, grade, country, myTeams, CancellationToken.None).ConfigureAwait(false);
  }
  /// Same as above, plus following
  /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
  /// <returns>List of Teams</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  protected virtual async Task<List<RETeam>> GetTeamsAsync(IEnumerable<int> id, IEnumerable<string> number, IEnumerable<int> @event, bool? registered, IEnumerable<int> program, IEnumerable<Grade> grade, IEnumerable<string> country, bool? myTeams, CancellationToken cancellationToken)
  {
    var teams = new List<RETeam>();
    var urlBuilder = new StringBuilder();
    urlBuilder.Append(_baseUrl != null ? _baseUrl.TrimEnd('/') : "").Append("/teams?");
    if (id != null)
    {
      foreach (var item_ in id) { urlBuilder.Append(Uri.EscapeDataString("id[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (number != null)
    {
      foreach (var item_ in number) { urlBuilder.Append(Uri.EscapeDataString("number[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (@event != null)
    {
      foreach (var item_ in @event) { urlBuilder.Append(Uri.EscapeDataString("event[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (registered != null)
    {
      urlBuilder.Append(Uri.EscapeDataString("registered") + "=").Append(Uri.EscapeDataString(ConvertToString(registered, CultureInfo.InvariantCulture))).Append("&");
    }
    if (program != null)
    {
      foreach (var item_ in program) { urlBuilder.Append(Uri.EscapeDataString("program[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (grade != null)
    {
      foreach (var item_ in grade) { urlBuilder.Append(Uri.EscapeDataString("grade[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (country != null)
    {
      foreach (var item_ in country) { urlBuilder.Append(Uri.EscapeDataString("country[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (myTeams != null)
    {
      urlBuilder.Append(Uri.EscapeDataString("myTeams") + "=").Append(Uri.EscapeDataString(ConvertToString(myTeams, CultureInfo.InvariantCulture))).Append("&");
    }
    urlBuilder.Length--;
    urlBuilder.Append(RobotEventConsts.REMaxPerPageTxt);

    var teamList = await GetItemsAsync<RETeam>(urlBuilder.ToString(), cancellationToken).ConfigureAwait(false);
    return teamList;

  }

  /// <param name="id">The ID of the Team</param>
  /// <param name="season">Filter by the Season in which the Award was given out</param>
  /// <returns>List of Awards</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<REAward>> GetAwardsAsync(int id, IEnumerable<int> season)
  {
    return await GetAwardsAsync(id, null, season, CancellationToken.None).ConfigureAwait(false);
  }
  /// Same as above, plus following
  /// <param name="@event">Filter by the Event at which the Award was given out</param>
  /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
  /// <returns>List of Awards</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  protected virtual async Task<List<REAward>> GetAwardsAsync(int id, IEnumerable<int> @event, IEnumerable<int> season, CancellationToken cancellationToken)
  {
    if (id == null)
      throw new System.ArgumentNullException("id");

    var urlBuilder = new System.Text.StringBuilder();
    urlBuilder.Append(_baseUrl != null ? _baseUrl.TrimEnd('/') : "").Append("/teams/{id}/awards?");
    urlBuilder.Replace("{id}", Uri.EscapeDataString(ConvertToString(id, CultureInfo.InvariantCulture)));
    if (@event != null)
    {
      foreach (var item_ in @event) { urlBuilder.Append(Uri.EscapeDataString("event[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (season != null)
    {
      foreach (var item_ in season) { urlBuilder.Append(Uri.EscapeDataString("season[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    urlBuilder.Length--;
    urlBuilder.Append(RobotEventConsts.REMaxPerPageTxt);

    var awardList = await GetItemsAsync<REAward>(urlBuilder.ToString(), cancellationToken).ConfigureAwait(false);
    return awardList;
  }

}
