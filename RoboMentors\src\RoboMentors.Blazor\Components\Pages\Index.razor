﻿@page "/"
@using Volo.Abp.MultiTenancy

@inherits RoboMentorsComponentBase
@inject AuthenticationStateProvider AuthenticationStateProvider

<Div class="container">
  <Div class="p-1 text-center">
    @if (CurrentUser.IsAuthenticated)
    {
      <h1>@L["Hello"] @CurrentUser.Name @CurrentUser.SurName!</h1>
    }
    <Div>
      <img src="/images/web/RoboMentor.png" style="max-width: 250px;" class="w-100" />
    </Div>
    <h1>@L["Welcome"]</h1>
    <p class="lead px-lg-5 mx-lg-5">@L[CurrentUser.IsAuthenticated ? "Welcome:Auth:Message" : "Welcome:UnAuth:Message"]</p>
    @if (!CurrentUser.IsAuthenticated)
    {
      <Div Class="row m-3">
        <Div Class="mx-auto">
          <Tooltip Class="mx-auto" Text="@L["Login:Tooltip"]" Placement="TooltipPlacement.Bottom">
            <a class="btn btn-primary" href="Account/Login"><font size="+2"><i class="fa fa-sign-in"></i>@L["Login"]</font></a>
          </Tooltip>
          <a class="btn btn-primary" href="/vex-robotics-world-championship"><font size="+2">@L["VexWorlds:Button"]</font></a>
        </Div>
      </Div>
      <Div Class="row">
        <Div Class="mx-auto">
          <Tooltip Class="mx-auto" Text="@L["Register:Tooltip"]" Placement="TooltipPlacement.Bottom">
            <a class="mx-auto" style="color: darkred" href="/UserRegistration">@L["Register"]</a>
          </Tooltip>
        </Div>
      </Div>
    } else
    {
      @if (IsDisabledJudge)
      {
        <Div Class="w-50 mx-auto bg-dark text-white text-center p-2">
          @L["Role:DisabledJudge:Message", CurrentUser.Name, CurrentUser.SurName]
        </Div>
      } else
      {
        <Div Class="row m-3">
          <Div Class="mx-auto">
            <a class="btn btn-primary" href="/vex-robotics-world-championship"><font size="+1">@L["VexWorlds:Button"]</font></a>
          </Div>
        </Div>
      }
    }
  </Div>

  <Card>
    <CardHeader Class="text-center text-primary">
      <h4><Strong>@L[HomePageQuestion]</Strong></h4>
    </CardHeader>
    <CardBody Class="py-0">
			@if (HomePageMasterImage.IsNullOrWhiteSpace() == false)
			{
				<CardImage Source="@HomePageMasterImage" Alt="@L[HomePageMasterTitle]"></CardImage>
			}
			<CardDeck Class="mx-auto">
        @foreach (var content in HomePageContents)
        {
          <Card Class="p-0 border-1 mx-auto" Style="max-width: 500px">
            @if (content.Image.IsNullOrWhiteSpace() == false)
            {
              <CardImage Source="@content.Image" Alt="@L[content.Title]" Style="max-width: 500px;" Class="w-100"></CardImage>
            }
            <CardBody Class="mx-auto p-1">
							<CardTitle Class="text-center" Size="4"><Strong>@L[content.Title]</Strong></CardTitle>
              <CardText Class="text-center">@L[content.Description]</CardText>
              <CardLink>
								@foreach (var link in content.Links)
								{
									<Button Color="Color.Primary" Block="true" Type="ButtonType.Link" To="@link.Href">@L[link.Label]</Button>
								}
              </CardLink>
            </CardBody>
          </Card>
        }
      </CardDeck>
    </CardBody>
    <CardFooter Class="text-center">
      <a href="https://kb.roboticseducation.org/hc/en-us/categories/*************-Volunteers?sc=get-started"
         target="_blank"
         class="btn btn-link px-1">@L["Role:RECLibrary:VolunteersLink"]</a>
    </CardFooter>
  </Card>
</Div>