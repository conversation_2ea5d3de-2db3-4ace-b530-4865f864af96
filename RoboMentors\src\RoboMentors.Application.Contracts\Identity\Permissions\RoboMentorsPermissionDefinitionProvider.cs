using RoboMentors.Localization;
using Volo.Abp.Authorization.Permissions;
using Volo.Abp.Localization;

namespace RoboMentors.Identity.Permissions;

public class RoboMentorsPermissionDefinitionProvider : PermissionDefinitionProvider
{
  public override void Define(IPermissionDefinitionContext context)
  {
    // -----
    // Event Management Group
    // -----
    var eventManagementGroup = context.AddGroup(RoboMentorsPermissions.EventManagementGroupName, L("Permission:EventManagement"));

    // Events
    var eventsPermission = eventManagementGroup.AddPermission(RoboMentorsPermissions.Events.Default, L("Permission:Events"));
    eventsPermission.AddChild(RoboMentorsPermissions.Events.Create, L("Permission:Events:Create"));
    eventsPermission.AddChild(RoboMentorsPermissions.Events.Edit, L("Permission:Events:Edit"));
    eventsPermission.AddChild(RoboMentorsPermissions.Events.Users, L("Permission:Events:Users"));
    eventsPermission.AddChild(RoboMentorsPermissions.Events.Delete, L("Permission:Events:Delete"));
    eventsPermission.AddChild(RoboMentorsPermissions.Events.Interview, L("Permission:Events:Interview"));

    // Regions
    var regionsPermission = eventManagementGroup.AddPermission(RoboMentorsPermissions.Regions.Default, L("Permission:Regions"));
    regionsPermission.AddChild(RoboMentorsPermissions.Regions.Create, L("Permission:Regions:Create"));
    regionsPermission.AddChild(RoboMentorsPermissions.Regions.Edit, L("Permission:Regions:Edit"));
    regionsPermission.AddChild(RoboMentorsPermissions.Regions.Delete, L("Permission:Regions:Delete"));
    regionsPermission.AddChild(RoboMentorsPermissions.Regions.Synchronize, L("Permission:Regions:Synchronize"));

    // -----
    // Judging Group
    // -----
    var judgingGroup = context.AddGroup(RoboMentorsPermissions.JudgingGroupName, L("Permission:Judging"));

    // Awards
    var awardsPermission = judgingGroup.AddPermission(RoboMentorsPermissions.Awards.Default, L("Permission:Awards"));
    awardsPermission.AddChild(RoboMentorsPermissions.Awards.Create, L("Permission:Awards:Create"));
    awardsPermission.AddChild(RoboMentorsPermissions.Awards.Edit, L("Permission:Awards:Edit"));
    awardsPermission.AddChild(RoboMentorsPermissions.Awards.Delete, L("Permission:Awards:Delete"));

    // Questions
    var questionsPermission = judgingGroup.AddPermission(RoboMentorsPermissions.Questions.Default, L("Permission:Questions"));
    questionsPermission.AddChild(RoboMentorsPermissions.Questions.Create, L("Permission:Questions:Create"));
    questionsPermission.AddChild(RoboMentorsPermissions.Questions.Edit, L("Permission:Questions:Edit"));
    questionsPermission.AddChild(RoboMentorsPermissions.Questions.Delete, L("Permission:Questions:Delete"));
  }

  private static LocalizableString L(string name)
  {
    return LocalizableString.Create<RoboMentorsResource>(name);
  }
}
