﻿using Microsoft.EntityFrameworkCore;
using RoboMentors.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;

namespace RoboMentors.Judging.Awards;

public class EfCoreAwardRepository
    : EfCoreRepository<RoboMentorsDbContext, Award, int>,
        IAwardRepository
{
  public EfCoreAwardRepository(
      IDbContextProvider<RoboMentorsDbContext> dbContextProvider)
      : base(dbContextProvider)
  {
  }

  public async Task<Award?> FindByKeywordAsync(string keyword)
  {
    var dbSet = await GetDbSetAsync();
    return await dbSet.FirstOrDefaultAsync(x => x.Keyword == keyword);
  }

  public async Task<List<Award>> GetListAsync(
      int skipCount,
      int maxResultCount,
      string sorting,
      string? filter = null)
  {
    var dbSet = await GetDbSetAsync();
    return await dbSet
        .WhereIf(filter.IsNullOrWhiteSpace() == false, x => x.Keyword.Contains(filter))
        .OrderBy(sorting)
        .Skip(skipCount)
        .Take(maxResultCount)
        .ToListAsync();
  }
}
