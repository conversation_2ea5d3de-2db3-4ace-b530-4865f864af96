﻿using RoboMentors.EventManagement.EventAwards;
using RoboMentors.EventManagement.RobotEvents;
using RoboMentors.Judging.Awards;
using RoboMentors.Judging.Questions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Identity;

namespace RoboMentors;

public class RoboMentorsV3DataSeederContributor : IDataSeedContributor, ITransientDependency
{
  //private readonly IEventRepository _eventRepository;
  //private readonly IEventDivisionRepository _eventDivisionRepository;
  //private readonly IEventPlanningDivisionRepository _eventPlanningDivisionRepository;
  //private readonly IEventAwardRepository _eventAwardRepository;
  //private readonly IEventMatchRepository _eventMatchRepository;
  //private readonly IEventRankingRepository _eventRankingRepository;
  //private readonly IEventSkillRepository _eventSkillRepository;
  //private readonly IEventTeamRepository _eventTeamRepository;
  //private readonly IEventUserRepository _eventUserRepository;
  private readonly IAwardRepository _awardRepository;
  private readonly AwardManager _awardManager;
  //private readonly IInterviewRepository _interviewRepository;
  //private readonly InterviewManager _interviewManager;
  private readonly IQuestionRepository _questionRepository;
  private readonly IIdentityUserRepository _userRepository;
  private readonly IIdentityRoleRepository _roleRepository;

  public RoboMentorsV3DataSeederContributor(
      //IEventRepository eventRepository,
      //IEventDivisionRepository eventDivisionRepository,
      //IEventPlanningDivisionRepository eventPlanningDivisionRepository,
      //IEventAwardRepository eventAwardRepository,
      //IEventMatchRepository eventMatchRepository,
      //IEventRankingRepository eventRankingRepository,
      //IEventSkillRepository eventSkillRepository,
      //IEventTeamRepository eventTeamRepository,
      //IEventUserRepository eventUserRepository,
      IAwardRepository awardRepository,
      AwardManager awardManager,
      //IInterviewRepository interviewRepository,
      //InterviewManager interviewManager,
      IQuestionRepository questionRepository,
      IIdentityUserRepository userRepository,
      IIdentityRoleRepository roleRepository)
  {
    //_eventRepository = eventRepository;
    //_eventDivisionRepository = eventDivisionRepository;
    //_eventPlanningDivisionRepository = eventPlanningDivisionRepository;
    //_eventAwardRepository = eventAwardRepository;
    //_eventMatchRepository = eventMatchRepository;
    //_eventRankingRepository = eventRankingRepository;
    //_eventSkillRepository = eventSkillRepository;
    //_eventTeamRepository = eventTeamRepository;
    //_eventUserRepository = eventUserRepository;
    _awardRepository = awardRepository;
    _awardManager = awardManager;
    //_interviewRepository = interviewRepository;
    //_interviewManager = interviewManager;
    _questionRepository = questionRepository;
    _userRepository = userRepository;
    _roleRepository = roleRepository;
  }

  public async Task SeedAsync(DataSeedContext context)
  {
    // User Management
    var users = await _userRepository.GetListAsync().ConfigureAwait(false);
    var user = users.FirstOrDefault();
    var roles = await _roleRepository.GetListAsync().ConfigureAwait(false);
    var role = roles.FirstOrDefault();

    //// Seed Event Data Here.
    //var eventId = 47924;
    //var eventSku = "RE-VRC-22-7924";
    //await SeedEventAsync(eventId, user, role).ConfigureAwait(false);

    // Seed Judging Data Here.
    await SeedAwardsAsync().ConfigureAwait(false);
    await SeedQuestionsAsync().ConfigureAwait(false);
  }

  public static byte[] StringToByteArray(string hex)
  {
    int length = hex.Length;
    byte[] bytes = new byte[length / 2];
    for (int i = 0; i < length; i += 2)
    {
      bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
    }
    return bytes;
  }


  //protected virtual async Task SeedEventAsync(int eventId, IdentityUser user, IdentityRole role)
  //{
  //  // Event Management Data Here.
  //  //*************************************************************************************************************
  //  //* Events Table - Start
  //  //*************************************************************************************************************
  //  if (await _eventRepository.GetCountAsync() <= 0)
  //  {
  //    string hexString = "1F8B080000000000000AA5925B4FE3301085FF0A9A57926027756E6FA8A01552B968E382568807AB7183A5E0AC1C07B15BF5BFEF386920740B12E2254ACE9CF1F9669C0D5C9490C3E993346A254EE68FF8AC1AF0E0B456A2952DE4F7B02C4EE6525B236A78F060DE74F8AEFAD206AEC493C4FEA5565696478515160BCE54F672015B6C7126B46FE0A2B88EA38C2E84AE3A51C99D4B6AECF821B543C0CF5DD6115778B40778A62E85292795511A2D67E24FADAA473BB18CD260D97AF053AEA5917A257B0159B8A89CFF8CE30177A2AE071D4212463EC97C9A71CA7212E661189088A531633E41814C90AED7EB56BA549FC458198A6372219E95AE706C20F4B578D3B4EA85FFBD15B52A7F4961207779AFBA032AF8718C54DE651460B277496940833EB5D346B57B8C181CB19C66C12C89C3244CDE183B3DB04D074A7396E4240D48C6665936DB99FBEDFC36B2C5DD09AB9EE54DA334B66E60819FB6739734A341CA3C5834BADA297E9A0431C35EB7386E846E95558D1E7E8B4F97F0E9FAA6D7B2B42BF49AB729229F869CA483F9786C41DBB92E4713A5381627C93BD3D63BC844BEC4541C623A18F79E09F78C3EB20FFE01D3B7F7F441DC3E93038FFEDBD3C3F61F34B4361912040000";
  //    byte[] byteArray = StringToByteArray(hexString);
  //    var ev = new Event(eventId)
  //    {
  //      Sku = "RE-VRC-22-7924",
  //      Name = "2023 Wisconsin State Championship - VRC High School",
  //      Start = DateTimeOffset.Parse("2023-03-10T00:00:00-05:00"),
  //      End = DateTimeOffset.Now.AddDays(1),  //DateTimeOffset.Parse("2023-03-11T00:00:00-05:00"),
  //      SeasonId = 173,
  //      SeasonName = "VRC 2022-2023: Spin Up",
  //      Level = EventLevel.Regional,
  //      Type = EventType.Tournament,
  //      Program = EventProgram.VRC,
  //      TimezoneBytes = byteArray,
  //      Venue = "Resch Expo",
  //      Address_1 = "840 Armed Forces Drive",
  //      Address_2 = string.Empty,
  //      City = "Ashwaubenon",
  //      Region = "Wisconsin",
  //      Postcode = "54304",
  //      Country = "United States",
  //      Latitude = 0,
  //      Longitude = 0,
  //      Capacity = 48,
  //      PrimaryName = string.Empty,
  //      PrimaryEmail = string.Empty,
  //      PrimaryPhone = string.Empty,
  //      TargetJudgeCount = 12,
  //      JudgeInterviewTeamCount = 5,
  //      InterviewTimeslotsJson = null,
  //      InitialInterviewDefaultMinutes = 8,
  //      AllowJudging = true,
  //      DifferentPlanningDivisions = false,
  //      RemoteJudging = false,
  //      RemoteJudgingStart = DateTimeOffset.Parse("2023-03-10T00:00:00-05:00"),
  //      RemoteJudgingEnd = DateTimeOffset.Parse("2023-03-11T00:00:00-05:00")
  //    };
  //    await _eventRepository.InsertAsync(ev);
  //    var newDivision = new EventDivision(eventId, 1, "Default", 1);
  //    await _eventDivisionRepository.InsertAsync(newDivision);
  //    var newPlanningDivision = new EventPlanningDivision(eventId, 1, "Default", 1, 1);
  //    await _eventPlanningDivisionRepository.InsertAsync(newPlanningDivision);
  //    var newAward = new EventAward(eventId, 1, 1, 1, "Excellence", "none", AwardType.Judged, false, 1, false, 1, 10, Grade.All, string.Empty, Guid.Empty, AwardLevel.Event);
  //    await _eventAwardRepository.InsertAsync(newAward);
  //    newAward = new EventAward(eventId, 2, 0, 1, "Champions", "none", AwardType.Performance, false, 1, false, 1, 10, Grade.All, string.Empty, Guid.Empty, AwardLevel.Event);
  //    await _eventAwardRepository.InsertAsync(newAward);
  //    var newTeam = new EventTeam(eventId, "4082B", "Freedom", "robot", "org", "Freedom", "Wisconsin", "United States", Grade.High_School, 1, JudgeTeam.Unassigned, null, ENRating.NotSubmitted, "den link", null, true, true, JudgeTeam.Unassigned, null);
  //    await _eventTeamRepository.InsertAsync(newTeam);
  //    newTeam = new EventTeam(eventId, "5062X", "Xenofox", "robot", "org", "Appleton", "Wisconsin", "United States", Grade.High_School, 1, JudgeTeam.Unassigned, null, ENRating.NotSubmitted, "den link", null, true, true, JudgeTeam.Unassigned, null);
  //    await _eventTeamRepository.InsertAsync(newTeam);
  //    var newMatch = new EventMatch(eventId, 1, 1, 1, 1, 1, DateTimeOffset.Now, DateTimeOffset.Now, "field", true, "4082B", "5062X", 20, "5509A", "5509B", 50);
  //    await _eventMatchRepository.InsertAsync(newMatch);
  //    newMatch = new EventMatch(eventId, 2, 2, 2, 2, 2, DateTimeOffset.Now, DateTimeOffset.Now, "field 2", true, "4082A", "5062A", 50, "5509C", "5509D", 20);
  //    await _eventMatchRepository.InsertAsync(newMatch);
  //    var newRanking = new EventRanking(eventId, 1, 1, "4082B", 1, 1, 1, 1, 1, 1, 1, 1, 1);
  //    await _eventRankingRepository.InsertAsync(newRanking);
  //    newRanking = new EventRanking(eventId, 2, 1, "5062X", 1, 1, 1, 1, 1, 1, 1, 1, 1);
  //    await _eventRankingRepository.InsertAsync(newRanking);
  //    var newSkill = new EventSkill(eventId, "4082B", 1, 3, 100, 3, 150);
  //    await _eventSkillRepository.InsertAsync(newSkill);
  //    newSkill = new EventSkill(eventId, "5062X", 2, 2, 150, 2, 100);
  //    await _eventSkillRepository.InsertAsync(newSkill);
  //    var newUser = new EventUser(eventId, user.Id, role.Id, Status.Committed, false, DateTimeOffset.MaxValue, 1, JudgeTeam.Unassigned, 0, JudgeTeam.Unassigned, JudgeTeam.Unassigned);
  //    await _eventUserRepository.InsertAsync(newUser);
  //  }
  //  // Event Interviews
  //  var eventInterviews = await _interviewRepository.GetListAsync(eventId).ConfigureAwait(false);
  //  if (eventInterviews.Count == 0)
  //  {
  //    var newInterview = new Interview(Guid.NewGuid())
  //    {
  //      EventId = eventId,
  //      TeamNumber = "4082B",
  //      PlanningDivisionId = 1,
  //      Type = InterviewType.Initial,
  //      InterviewTeam = JudgeTeam.A,
  //      Status = JudgingStatus.Started,
  //      Start = DateTimeOffset.Now,
  //      Completed = null,
  //      Notes = "notes-4082B",
  //      TIR_EDP = 5,
  //      TIR_Strategy = 4.75,
  //      TIR_RobotDesign = 4.5,
  //      TIR_RobotBuild = 4.25,
  //      TIR_Programming = 4,
  //      TIR_Project = 3.75,
  //      TIR_Teamwork = 3.5,
  //      TIR_Respect = 3.25,
  //      TIR_Creative = 3,
  //      Accomplishments = 2,
  //      Obstacles = 1,
  //      SpecialNotes = "special-4082B",
  //      VersionControl = true,
  //      AwardRecommendations = ""
  //    };
  //    await _interviewRepository.InsertAsync(newInterview);
  //    newInterview = new Interview(Guid.NewGuid())
  //    {
  //      EventId = eventId,
  //      TeamNumber = "5062X",
  //      PlanningDivisionId = 2,
  //      Type = InterviewType.Initial,
  //      InterviewTeam = JudgeTeam.A,
  //      Status = JudgingStatus.Scored,
  //      Start = DateTimeOffset.Now,
  //      Completed = DateTimeOffset.Now,
  //      Notes = "notes-5062X",
  //      TIR_EDP = 1,
  //      TIR_Strategy = 1.25,
  //      TIR_RobotDesign = 1.5,
  //      TIR_RobotBuild = 1.75,
  //      TIR_Programming = 2,
  //      TIR_Project = 2.25,
  //      TIR_Teamwork = 2.5,
  //      TIR_Respect = 2.75,
  //      TIR_Creative = 3,
  //      Accomplishments = 4,
  //      Obstacles = 5,
  //      SpecialNotes = "special-4082B",
  //      VersionControl = true,
  //      AwardRecommendations = ""
  //    };
  //    await _interviewRepository.InsertAsync(newInterview);
  //    newInterview = new Interview(Guid.NewGuid())
  //    {
  //      EventId = eventId,
  //      TeamNumber = "5509G",
  //      PlanningDivisionId = 1,
  //      Type = InterviewType.FollowUp,
  //      InterviewTeam = JudgeTeam.Z,
  //      Status = JudgingStatus.Ranked,
  //      Start = DateTimeOffset.Now,
  //      Completed = DateTimeOffset.Now,
  //      Notes = "notes-5509G",
  //      TIR_EDP = 1,
  //      TIR_Strategy = 1.25,
  //      TIR_RobotDesign = 1.5,
  //      TIR_RobotBuild = 1.75,
  //      TIR_Programming = 2,
  //      TIR_Project = 2.25,
  //      TIR_Teamwork = 2.5,
  //      TIR_Respect = 2.75,
  //      TIR_Creative = 3,
  //      Accomplishments = 4,
  //      Obstacles = 5,
  //      SpecialNotes = "special-5509G",
  //      VersionControl = true,
  //      AwardRecommendations = ""
  //    };
  //    await _interviewRepository.InsertAsync(newInterview);
  //  }
  //}

  protected virtual async Task SeedAwardsAsync()
  {
    //*************************************************************************************************************
    //* Awards Table - Start
    //*************************************************************************************************************
    var awardId = 0;
    // Excellence Award
    awardId = 1;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Excellence",
          abbrev: "E",
          title: "Excellence Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: false,
          includeOnFollowUpRanking: false,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 1,
          viqOrder: 1
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Design Award
    awardId = 2;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Design",
          abbrev: "D",
          title: "Design Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: false,
          includeOnFollowUpRanking: true,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 2,
          viqOrder: 2
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Innovate Award
    awardId = 3;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Innovate",
          abbrev: "I",
          title: "Innovate Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: false,
          includeOnFollowUpRanking: false,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 3,
          viqOrder: 3
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Think Award
    awardId = 4;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Think",
          abbrev: "T",
          title: "Think Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: true,
          includeOnFollowUpRanking: true,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 4,
          viqOrder: 5
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Amaze Award
    awardId = 5;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Amaze",
          abbrev: "A",
          title: "Amaze Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: true,
          includeOnFollowUpRanking: true,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 5,
          viqOrder: 6
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Build Award
    awardId = 6;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Build",
          abbrev: "B",
          title: "Build Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: true,
          includeOnFollowUpRanking: true,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 6,
          viqOrder: 7
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Create Award
    awardId = 7;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Create",
          abbrev: "C",
          title: "Create Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: true,
          includeOnFollowUpRanking: true,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 7,
          viqOrder: 4
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Judges Award
    awardId = 8;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Judges",
          abbrev: "J",
          title: "Judges Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: true,
          includeOnFollowUpRanking: true,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 8,
          viqOrder: 8
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Inspire Award
    awardId = 9;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Inspire",
          abbrev: "Ins",
          title: "Inspire Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: false,
          includeOnFollowUpRanking: false,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 9,
          viqOrder: 9
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Sportsmanship Award
    awardId = 10;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Sportsmanship",
          abbrev: "S",
          title: "Sportsmanship Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: false,
          includeOnFollowUpRanking: false,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 10,
          viqOrder: 10
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    // Energy Award
    awardId = 11;
    if (await _awardRepository.FindAsync(awardId) == null)
    {
      var result = await _awardManager.CreateAsync(
          id: awardId,
          keyword: "Energy",
          abbrev: "En",
          title: "Energy Award",
          program: EventProgram.All,
          type: AwardType.Judged,
          description: "Description",
          script: "Script",
          icon: "icon",
          includeOnInitialRanking: false,
          includeOnFollowUpRanking: false,
          followUpInterviewDefaultMinutes: 10,
          defaultOrder: 11,
          viqOrder: 11
      );
      if (result.Success == true && result.Data != null)
      {
        await _awardRepository.InsertAsync(result.Data);
      }
      else
      {
        throw new InvalidOperationException(result.ErrorMessage);
      }
    }
    //*************************************************************************************************************
    //* Awards Table - End
    //*************************************************************************************************************
  }


  protected virtual async Task SeedQuestionsAsync()
  {
    //*************************************************************************************************************
    //* Questions Table - Start
    //*************************************************************************************************************
    if (await _questionRepository.GetCountAsync() <= 0)
    {
      List<Question> questions = new();
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 1, NoteKeyword = "", Text = "Outside of your robot, what part of your team are you most proud of? (Judges Award)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 2, NoteKeyword = "", Text = "Tell us how your team developed your robot design. (Design, Build Awards)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 3, NoteKeyword = "", Text = "(a) Did you use any sensors?  (b) What are they used for?  (c) Do you have an autonomous mode?  (Think Award)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 4, NoteKeyword = "", Text = "(a) Tell us how your team developed your game play and skills strategies.  (b) What are your target Driving and Autonomous Coding Skills scores?  (Amaze, Think Awards)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 5, NoteKeyword = "", Text = "(a) Tell us about the most creative design solution on your robot.  (b) Did you document the solution in your engineering notebook?  (Create, Innovate Awards)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 6, NoteKeyword = "", Text = "Tell us how you designed your robot to be rugged, durable, and  safe for the competition.   (Build Award)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 7, NoteKeyword = "", Text = "Tell us how your team tracked your progress against your overall project timeline. (Design, Innovate Awards)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Initial, AwardId = 99, Order = 8, NoteKeyword = "", Text = "Is there anything else you would like to share with us?  (Team, Design Process, Game Strategy, etc.)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 1, Order = 1, NoteKeyword = "", Text = "Tell us about your team’s design process and how it is recorded in your notebook." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 1, Order = 2, NoteKeyword = "", Text = "Tell us about the game strategies your team considered and why the current one was chosen." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 1, Order = 3, NoteKeyword = "", Text = "Tell us how your team tracked your progress against your overall project timeline." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 1, Order = 4, NoteKeyword = "", Text = "Tell us about how you assigned tasks when you got together as a team." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 1, Order = 5, NoteKeyword = "", Text = "Tell us about how you managed your resources, including your time, parts, money." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 1, Order = 6, NoteKeyword = "", Text = "Tell us about the biggest challenge you have faced outside of your robot." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 2, Order = 1, NoteKeyword = "", Text = "Tell us about your team’s design process and how it is recorded in your notebook." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 2, Order = 2, NoteKeyword = "", Text = "Tell us about the game strategies your team considered and why the current one was chosen." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 2, Order = 3, NoteKeyword = "", Text = "Tell us how your team tracked your progress against your overall project timeline." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 2, Order = 4, NoteKeyword = "", Text = "Tell us about how you assigned tasks when you got together as a team." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 2, Order = 5, NoteKeyword = "", Text = "Tell us about how you managed your resources, including your time, parts, money." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 2, Order = 6, NoteKeyword = "", Text = "Tell us about the biggest challenge you have faced outside of your robot." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 1, NoteKeyword = "", Text = "Tell us about your team’s design process and how it is recorded in your notebook." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 2, NoteKeyword = "", Text = "Tell us about the game strategies your team considered and why the current one was chosen." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 3, NoteKeyword = "", Text = "Tell us about the part of your team’s design or strategy or other feature that is not common among the teams at this event.  How is it different?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 4, NoteKeyword = "", Text = "Tell us how you determine its effectiveness compared with more common solutions." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 5, NoteKeyword = "", Text = "Tell us about the challenges of implementing (name unique feature)." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 6, NoteKeyword = "", Text = "Tell us how your team tracked your progress against your overall project timeline." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 3, Order = 7, NoteKeyword = "", Text = "Tell us about the biggest challenge you have faced outside of your robot." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 1, NoteKeyword = "", Text = "(a) Tell us about your autonomous match and skills strategies.  (b) What are your best scores for each?  (c) What are your average scores for each?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 2, NoteKeyword = "", Text = "What is the hardest thing that you had to program for this year?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 3, NoteKeyword = "", Text = "Tell us about any unique or special features you have in your code.  Explain the code for that feature." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 4, NoteKeyword = "", Text = "How did you keep track of the different versions of your code?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 5, NoteKeyword = "", Text = "Tell us about the sensors on your robot, and how your programming uses them." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 6, NoteKeyword = "", Text = "(a) When programming your autonomous match and skills, how do you measure your movements? (inertial, distance, rotation, time) (b) Why did you choose that?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 7, NoteKeyword = "", Text = "(a) Do you utilize the V5s touch screen?  (b) How?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 8, NoteKeyword = "", Text = "Optional, if time permits:  Did you learn any new coding techniques that you were able to add into your programming this year?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 9, NoteKeyword = "", Text = "Optional, if time permits:  Was your robot design limited by your programming knowledge?  How?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 4, Order = 10, NoteKeyword = "", Text = "Optional, if time permits:  Do you utilize Object Oriented Programming?  PID programming?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 1, NoteKeyword = "", Text = "Tell us about your autonomous match and skills strategies." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 2, NoteKeyword = "", Text = "(a) Was your robot design limited by your programming knowledge?  (b) How?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 3, NoteKeyword = "", Text = "What do you attribute your consistent scoring to?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 4, NoteKeyword = "", Text = "What skills scores are you getting when you practice for both Driving and Autonomous Skills?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 5, NoteKeyword = "", Text = "What skills scores are you getting during competitions for both Driving and Autonomous Skills?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 6, NoteKeyword = "", Text = "How many times do you practice your Driving Skills at a meeting?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 5, Order = 7, NoteKeyword = "", Text = "How many times do you practice your Autonomous Coding Skills at a meeting?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 1, NoteKeyword = "", Text = "What techniques did you use in designing your robot to stand up to tough match play?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 2, NoteKeyword = "", Text = "Did you learn any new techniques for reinforcement?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 3, NoteKeyword = "", Text = "Is there anything that you would do to make your robot even stronger?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 4, NoteKeyword = "", Text = "If a lock collar (insert part of choice) falls off your robot during a match, where would you look first to put it back on?  (Shows they know their design fully) " });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 5, NoteKeyword = "", Text = "Tell us about a subsystem on your robot that one person designed, and another teammate refined?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 6, NoteKeyword = "", Text = "What things do you do to refine your robot? (Examples: smaller nyloks, proper length screws, aluminum fasteners, 1x1 instead of 1x5)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 6, Order = 7, NoteKeyword = "", Text = "Tell us about the most dangerous part of your robot and how you stay safe." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 7, Order = 1, NoteKeyword = "", Text = "Tell us about the part of your team’s robot or strategy that is special to your team. How is it different?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 7, Order = 2, NoteKeyword = "", Text = "How did you come up with (name unique feature)?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 7, Order = 3, NoteKeyword = "", Text = "Were there other robots or real-world examples that inspired your solution?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 7, Order = 4, NoteKeyword = "", Text = "How did you determine if the (name unique feature) was better? How much better is it?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 7, Order = 5, NoteKeyword = "", Text = "Do you think that having a unique robot made it easier or harder to build a competitive robot?  Why?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 7, Order = 6, NoteKeyword = "", Text = "Did creating (name unique feature) help you think of more creative ideas? What ideas do you want to work on next?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.Award, AwardId = 8, Order = 1, NoteKeyword = "", Text = "Tell us about the biggest challenge you have faced outside of your robot and how you overcame it." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 1, NoteKeyword = "", Text = "You are at Worlds!  Apart from your robot, what part of your team are you most proud of?  (Judges)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 2, NoteKeyword = "", Text = "Tell us about your game strategies and why the current one was chosen. (Design/Create/Amaze)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 3, NoteKeyword = "", Text = "Tell us about your team roles, resources and how your team gets things done? (Design)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 4, NoteKeyword = "", Text = "Tell us how your team tracked its progress and organized its work over the season. (Design)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 5, NoteKeyword = "", Text = "Tell us about the favorite feature of your Robot or Programming?  (Innovate/Create/Build/Think)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 6, NoteKeyword = "", Text = "Tell us about how your team developed this feature (from answer above). (Design Process)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 7, NoteKeyword = "", Text = "How does your use of sensors or programming improve autonomous and driver scores? (Think/Amaze)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 8, NoteKeyword = "", Text = "What have you done with your building and programming to make your robot consistent and reliable? (Think/Amaze/Build)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 9, NoteKeyword = "", Text = "Is there anything unique or different about your robot? (Create)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 10, NoteKeyword = "", Text = "What has your team learned over the course of this season and what challenges have you worked together to overcome? (Judges / Inspire)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 11, NoteKeyword = "", Text = "Is there anything else about your robot or your team that you would like to share with us?" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.FollowUp, AwardId = 99, Order = 12, NoteKeyword = "", Text = "This may not apply to your team, but did you provide a submission for the Innovate Award?  (if yes)  BRIEFLY tell us about it? (Innovate)" });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 1, NoteKeyword = "Introduction", Text = "BRIEFLY introduce yourselves. Please tell us your name & what your role is on your team." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 2, NoteKeyword = "Accomplishments", Text = "Tell us a few of the things your team is proud about this season - some of the things your team has accomplished." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 3, NoteKeyword = "Obstacles", Text = "Tell us about a few of the challenges your team encountered this season, and how did you overcome them." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 4, NoteKeyword = "Strategy", Text = "Tell us your process for developing game plans and skills strategies." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 5, NoteKeyword = "Robot Design", Text = "Tell us your process for creating your robot's design." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 6, NoteKeyword = "Build", Text = "Tell us your process for ensuring your robot is reliable and well built for competition." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 7, NoteKeyword = "Create", Text = "Tell us about the most creative design solution on your robot." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 8, NoteKeyword = "Think", Text = "Tell us about your process for programming and/or coding autonomously: including how you used any sensors." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 9, NoteKeyword = "Version Control", Text = "Tell us your process for tracking changes you have made in your robot's programming this season." });
      questions.Add(new Question(new Guid()) { InterviewType = Judging.Interviews.InterviewType.RemoteInitial, AwardId = 99, Order = 10, NoteKeyword = "Team", Text = "Is there anything else you would like to share with us regarding your team, your robot design, or your game strategy? (Optional)" });

      foreach (var question in questions)
      {
        await _questionRepository.InsertAsync(question, autoSave: true);
      }
    }
    //*************************************************************************************************************
    //* Questions Table - End
    //*************************************************************************************************************

  }
}
