﻿<Project Sdk="Microsoft.NET.Sdk">

  <Import Project="..\..\common.props" />

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <RootNamespace>RoboMentors</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\RoboMentors.Domain\RoboMentors.Domain.csproj" />
    <ProjectReference Include="..\RoboMentors.Application.Contracts\RoboMentors.Application.Contracts.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Volo.Abp.PermissionManagement.Application" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.FeatureManagement.Application" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.SettingManagement.Application" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Account.Application" Version="9.3.0" />
    <PackageReference Include="Volo.Abp.Identity.Application" Version="9.3.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Identity\Roles\" />
  </ItemGroup>

</Project>
