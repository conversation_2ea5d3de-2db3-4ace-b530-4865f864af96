parameters:
- name: projectPath
  type: string
- name: outputDir
  type: string
- name: artifactName
  type: string
- name: zipArtifact
  type: boolean
  default: false
- name: excludeFiles
  type: object
  default: []

steps:
- task: DotNetCoreCLI@2
  displayName: 'Publish ${{ parameters.artifactName }}'
  inputs:
    command: 'publish'
    publishWebProjects: false
    projects: '${{ parameters.projectPath }}'
    arguments: '--configuration $(BuildConfiguration) --output $(Build.ArtifactStagingDirectory)/${{ parameters.outputDir }} --no-build'

- ${{ if ne(length(parameters.excludeFiles), 0) }}:
  - task: DeleteFiles@1
    displayName: 'Remove excluded files from ${{ parameters.artifactName }}'
    inputs:
      SourceFolder: '$(Build.ArtifactStagingDirectory)/${{ parameters.outputDir }}'
      Contents: |
        ${{ each file in parameters.excludeFiles }}:
        ${{ file }}

- ${{ if eq(parameters.zipArtifact, true) }}:
  - task: ArchiveFiles@2
    displayName: 'Archive ${{ parameters.artifactName }}'
    inputs:
      rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/${{ parameters.outputDir }}'
      includeRootFolder: false
      archiveType: 'zip'
      archiveFile: '$(Build.ArtifactStagingDirectory)/${{ parameters.artifactName }}.zip'
      replaceExistingArchive: true

- task: PublishBuildArtifacts@1
  displayName: 'Publish ${{ parameters.artifactName }} Artifact'
  inputs:
    PathtoPublish: '${{ if eq(parameters.zipArtifact, true) }}$(Build.ArtifactStagingDirectory)/${{ parameters.artifactName }}.zip${{ else }}$(Build.ArtifactStagingDirectory)/${{ parameters.outputDir }}${{ endif }}'
    ArtifactName: '${{ parameters.artifactName }}'
