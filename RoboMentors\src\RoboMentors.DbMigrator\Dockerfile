FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser

# Copy the published application
COPY bin/Release/net9.0/publish/ ./

# Set ownership and permissions
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production

ENTRYPOINT ["dotnet", "RoboMentors.DbMigrator.dll"]