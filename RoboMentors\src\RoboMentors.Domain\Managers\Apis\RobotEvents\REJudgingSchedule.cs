﻿using System;

namespace RoboMentors.Managers.Apis.RobotEvents;

[System.CodeDom.Compiler.GeneratedCode("NJsonSchema", "10.3.1.0 (Newtonsoft.Json v12.0.0.0)")]
public partial class REJudgingSchedule
{
  [Newtonsoft.Json.JsonProperty("Created", Required = Newtonsoft.Json.Required.Always)]
  public string Created { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Team", Required = Newtonsoft.Json.Required.Always)]
  [System.ComponentModel.DataAnnotations.Required(AllowEmptyStrings = true)]
  public string Team { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Program", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Program { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Grade Level", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string GradeLevel { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Interview Time", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string InterviewTime { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Interview End Time", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string InterviewEndTime { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Region Group", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string RegionGroup { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Language", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Include)]
  public string? Lauguage { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Link", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Include)]
  public string? Link { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Primary Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string PrimaryPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Secondary Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SecondaryPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Country/Location", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string CountryLocation { get; set; } = String.Empty;

  [Newtonsoft.Json.JsonProperty("Region", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string Region { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Postal Code", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string PostalCode { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("City", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string City { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Primary Coach Name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string PrimaryContactName { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Primary Coach Email", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string PrimaryContactEmail { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Primary Coach Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string PrimaryContactPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Primary Coach Emergency Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string PrimaryContactEmergencyPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Secondary Coach Name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SecondaryContactName { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Secondary Coach Email", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SecondaryContactEmail { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Secondary Coach Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SecondaryContactPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Secondary Coach Emergency Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string SecondaryContactEmergencyPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Financial Contact Name", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string FinancialContactName { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Financial Contact Email", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string FinancialContactEmail { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Financial Contact Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string FinancialContactPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Financial Contact Emergency Phone", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string FinancialContactEmergencyPhone { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Translator Needed?", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string TranslatorNeedeed { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Team Language", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string TeamLanguage { get; set; } = string.Empty;

  [Newtonsoft.Json.JsonProperty("Earlier/Later Preference", Required = Newtonsoft.Json.Required.Default, NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
  public string EarlierLaterPreference { get; set; } = string.Empty;

}
