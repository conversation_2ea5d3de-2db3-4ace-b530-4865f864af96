﻿using RoboMentors.Judging.Interviews;
using System;
using Volo.Abp.Domain.Entities.Auditing;

namespace RoboMentors.Judging.Questions;

public class Question : AuditedEntity<Guid>
{
  public virtual InterviewType InterviewType { get; set; }

  public virtual int AwardId { get; set; }

  public virtual int Order { get; set; }

  public virtual string NoteKeyword { get; set; } = string.Empty;

  public virtual string Text { get; set; } = string.Empty;

  // Parameterless Constructor for ORMS
  protected Question()
  {
  }

  // Primary constructor
  public Question(Guid id) : base(id)
  {
    Id = id;
  }
}
