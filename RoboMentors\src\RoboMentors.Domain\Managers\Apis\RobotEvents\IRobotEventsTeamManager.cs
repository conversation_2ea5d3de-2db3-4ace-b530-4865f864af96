﻿using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RoboMentors.Managers.Apis.RobotEvents;

public interface IRobotEventsTeamManager
{
  Task<List<RETeam>> GetTeamsAsync(bool? registered, IEnumerable<int> program);
  Task<List<RETeam>> GetTeamsAsync(IEnumerable<string> number, IEnumerable<int> program);
  Task<List<RETeam>> GetTeamsAsync(IEnumerable<int> id, IEnumerable<string> number, IEnumerable<int> @event, bool? registered, IEnumerable<int> program, IEnumerable<Grade> grade, IEnumerable<string> country, bool? myTeams);
  Task<List<REAward>> GetAwardsAsync(int id, IEnumerable<int> season);
}
