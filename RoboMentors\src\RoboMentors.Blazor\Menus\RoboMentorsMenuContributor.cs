﻿using RoboMentors.Localization;
using RoboMentors.Identity.Permissions;
using System.Threading.Tasks;
using Volo.Abp.Identity.Blazor;
using Volo.Abp.SettingManagement.Blazor.Menus;
using Volo.Abp.UI.Navigation;

namespace RoboMentors.Blazor.Menus;

public class RoboMentorsMenuContributor : IMenuContributor
{
  public async Task ConfigureMenuAsync(MenuConfigurationContext context)
  {
    if (context.Menu.Name == StandardMenus.Main)
    {
      await ConfigureMainMenuAsync(context);
    }
  }

  private async Task ConfigureMainMenuAsync(MenuConfigurationContext context)
  {
    var l = context.GetLocalizer<RoboMentorsResource>();

    // **************************
    // * Home Menu
    // **************************
    context.Menu.Items.Insert(
      0,
      new ApplicationMenuItem(
          RoboMentorsMenus.Home,
          l["Menu:Home"],
          "/",
          icon: "fas fa-home",
          order: 1
      )
    );

    // **************************
    // * Event Management Menu
    // **************************
    // Check Awards PERMISSIONS before including it in the menu
    if (await context.IsGrantedAsync(RoboMentorsPermissions.Events.Default))
    {
      var eventManagementEvents = new ApplicationMenuItem(
          RoboMentorsMenus.EventManagement.Events,
          l["Menu:EventManagement:Events"],
          url: "/events",
          icon: "fas fa-calendar",
          order: 1
      );
      context.Menu.Items.Insert(1, eventManagementEvents);
    }

    // **************************
    // * Maintenance Menu
    // **************************
    var maintenanceMenu = new ApplicationMenuItem(
        RoboMentorsMenus.Maintenance.Menu,
        l["Menu:Maintenance"],
        icon: "fas fa-wrench",
        order: 2
    );
    context.Menu.Items.Insert(2, maintenanceMenu);

    // Check Awards PERMISSIONS before including it in the menu
    if (await context.IsGrantedAsync(RoboMentorsPermissions.Awards.Create) ||
        await context.IsGrantedAsync(RoboMentorsPermissions.Awards.Edit) ||
        await context.IsGrantedAsync(RoboMentorsPermissions.Awards.Delete))
    {
      var maintenanceAwards = new ApplicationMenuItem(
          RoboMentorsMenus.Maintenance.Judging.Awards,
          l["Menu:Maintenance:Judging:Awards"],
          url: "/maintenance/awards",
          icon: "fas fa-trophy"
      );
      maintenanceMenu.AddItem(maintenanceAwards);
    }

    // Check Questions PERMISSIONS before including it in the menu
    if (await context.IsGrantedAsync(RoboMentorsPermissions.Questions.Create) ||
        await context.IsGrantedAsync(RoboMentorsPermissions.Questions.Edit) ||
        await context.IsGrantedAsync(RoboMentorsPermissions.Questions.Delete))
    {
      var maintenanceQuestions = new ApplicationMenuItem(
        RoboMentorsMenus.Maintenance.Judging.Questions,
        l["Menu:Maintenance:Judging:Questions"],
        url: "/maintenance/questions",
        icon: "fas fa-question-circle"
      );
      maintenanceMenu.AddItem(maintenanceQuestions);
    }

    // check Regions PERMISSIONS before including it in the menu
    if (await context.IsGrantedAsync(RoboMentorsPermissions.Regions.Create) ||
        await context.IsGrantedAsync(RoboMentorsPermissions.Regions.Edit) ||
        await context.IsGrantedAsync(RoboMentorsPermissions.Regions.Delete))
    {
      var maintenanceRegions = new ApplicationMenuItem(
          RoboMentorsMenus.Maintenance.EventManagement.Regions,
          l["Menu:Maintenance:EventManagement:Regions"],
          url: "/maintenance/regions",
          icon: "fas fa-globe"
      );
      maintenanceMenu.AddItem(maintenanceRegions);
    }


    //Administration
    var administration = context.Menu.GetAdministration();
    administration.Order = 6;

    administration.SetSubItemOrder(IdentityMenuNames.GroupName, 2);
    administration.SetSubItemOrder(SettingManagementMenus.GroupName, 3);

    return;
  }
}
