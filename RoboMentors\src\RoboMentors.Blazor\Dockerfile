FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Create a non-root user
RUN adduser --disabled-password --gecos '' appuser

# Copy the published application
COPY bin/Release/net9.0/publish/ ./

# Copy the OpenIddict certificate (generated during build)
COPY openiddict.pfx ./

# Set ownership and permissions
RUN chown -R appuser:appuser /app
RUN chmod 644 /app/openiddict.pfx

# Switch to non-root user
USER appuser

# Set environment variables
ENV ASPNETCORE_URLS=http://+:80
ENV ASPNETCORE_ENVIRONMENT=Production

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/health-status || exit 1

ENTRYPOINT ["dotnet", "RoboMentors.Blazor.dll"]