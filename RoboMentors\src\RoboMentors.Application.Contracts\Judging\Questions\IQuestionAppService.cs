﻿using RoboMentors.Judging.Interviews;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Application.Services;

namespace RoboMentors.Judging.Questions;

public interface IQuestionAppService : IApplicationService
{
  // QuestionAppService Methods
  Task<QuestionDto> GetDtoAsync(Guid id);
  Task<List<QuestionDto>> GetDtosAsync(InterviewType? interviewType = null, int? awardId = null);
  Task<ApiResult<QuestionDto>> CreateAsync(CreateUpdateQuestionDto input);
  Task<ApiResult<QuestionDto>> UpdateAsync(CreateUpdateQuestionDto input);
  Task DeleteAsync(Guid id);
}
