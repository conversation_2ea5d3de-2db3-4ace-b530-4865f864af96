﻿using Volo.Abp.Identity;

namespace RoboMentors;

public static class RoboMentorsConsts
{
  // Entity Table Constants
  public const string DbTablePrefix = "App";
  public const string EventManagementDbTablePrefix = "Em";
  public const string VolunteerManagementDbTablePrefix = "Vm";
  public const string JudgingDbTablePrefix = "Jdg";
  public const string ReffingDbTablePrefix = "Ref";
  public const string TrainingDbTablePrefix = "Trn";

  public const string DbSchema = null;
  public const string EventManagementDbSchema = null;
  public const string VolunteerManagementDbSchema = null;
  public const string JudgingDbSchema = null;
  public const string ReffingDbSchema = null;
  public const string TrainingDbSchema = null;

  public const string AdminEmailDefaultValue = IdentityDataSeedContributor.AdminEmailDefaultValue;
  public const string AdminPasswordDefaultValue = IdentityDataSeedContributor.AdminPasswordDefaultValue;

}
