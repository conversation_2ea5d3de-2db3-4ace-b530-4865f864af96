﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace RoboMentors.Migrations
{
    /// <inheritdoc />
    public partial class ChangeKey_Teams : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_EmTeams",
                table: "EmTeams");

            migrationBuilder.DropIndex(
                name: "IX_Team_Program_Number",
                table: "EmTeams");

            migrationBuilder.DropColumn(
                name: "Id",
                table: "EmTeams");

            migrationBuilder.AlterColumn<string>(
                name: "ProgramText",
                table: "EmTeams",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(450)");

            migrationBuilder.AddPrimaryKey(
                name: "PK_EmTeams",
                table: "EmTeams",
                columns: new[] { "Program", "Number" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_EmTeams",
                table: "EmTeams");

            migrationBuilder.AlterColumn<string>(
                name: "ProgramText",
                table: "EmTeams",
                type: "nvarchar(450)",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<int>(
                name: "Id",
                table: "EmTeams",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddPrimaryKey(
                name: "PK_EmTeams",
                table: "EmTeams",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_Team_Program_Number",
                table: "EmTeams",
                columns: new[] { "ProgramText", "Number" },
                unique: true);
        }
    }
}
