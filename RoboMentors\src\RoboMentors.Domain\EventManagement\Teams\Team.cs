﻿using RoboMentors.EventManagement.RobotEvents;
using System;
using System.ComponentModel.DataAnnotations.Schema;
using Volo.Abp.Domain.Entities;

namespace RoboMentors.EventManagement.Teams;

public class Team : Entity
{
  public virtual int Id { get; set; }

  public virtual string Number { get; set; }

  public virtual string Name { get; set; }

  public virtual string RobotName { get; set; }

  public virtual string Organization { get; set; }

  public virtual string City { get; set; }

  public virtual string Region { get; set; }

  public virtual string Country { get; set; }

  public virtual Grade Grade { get; set; }

  public virtual EventProgram Program { get; set; }

  public virtual string ProgramText { get; set; }

  public virtual double Latitude { get; set; }
  public virtual double Longitude { get; set; }

  [NotMapped]
  public string AffiliateRegion => String.IsNullOrWhiteSpace(Region) == false ? Region : Country;


  public Team()
  {
  }

  public Team(
          int id,
          string number,
          string name,
          string robotName,
          string organization,
          string city,
          string region,
          string country,
          Grade grade,
          EventProgram program,
          string programText,
          double latitude,
          double longitude)
  {
    Id = id;
    Number = number;
    Name = name;
    RobotName = robotName;
    Organization = organization;
    City = city;
    Region = region;
    Country = country;
    Grade = grade;
    Program = program;
    ProgramText = programText;
    Latitude = latitude;
    Longitude = longitude;
  }

  public override object[] GetKeys()
  {
    return new object[] { Id };
  }

}
