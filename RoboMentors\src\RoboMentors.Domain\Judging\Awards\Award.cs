﻿using RoboMentors.EventManagement.EventAwards;
using RoboMentors.EventManagement.RobotEvents;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace RoboMentors.Judging.Awards;

public class Award : AuditedEntity<int>
{
  [Required]
  [StringLength(AwardConsts.KeywordMaxLength)]
  public virtual string Keyword { get; private set; } = string.Empty;

  [Required]
  [StringLength(AwardConsts.AbbrevMaxLength)]
  public virtual string Abbrev { get; set; } = string.Empty;

  [Required]
  public virtual string Title { get; set; } = string.Empty;

  public virtual EventProgram Program { get; set; }

  public virtual AwardType Type { get; set; }

  public virtual string Description { get; set; } = string.Empty;

  public virtual string Script { get; set; } = string.Empty;

  public virtual string Icon { get; set; } = string.Empty;

  public virtual bool IncludeOnInitialRanking { get; set; }

  public virtual bool IncludeOnFollowUpRanking { get; set; }

  public virtual int FollowUpInterviewDefaultMinutes { get; set; }

  public virtual int DefaultOrder { get; set; }

  public virtual int VIQOrder { get; set; }

  // Parameterless Constructor for ORMS
  protected Award()
  {
  }

  // Primary constructor
  internal Award(
      int id,
      [NotNull] string keyword,
      [NotNull] string abbrev,
      [NotNull] string title,
      EventProgram program,
      AwardType type,
      [NotNull] string description,
      [NotNull] string script,
      [NotNull] string icon,
      bool includeOnInitialRanking,
      bool includeOnFollowUpRanking,
      int followUpInterviewDefaultMinutes,
      int defaultOrder,
      int viqOrder) : base(id)
  {
    Id = id;
    SetKeyword(keyword);
    Abbrev = abbrev;
    Title = title;
    Program = program;
    Type = type;
    Description = description;
    Script = script;
    Icon = icon;
    IncludeOnInitialRanking = includeOnInitialRanking;
    IncludeOnFollowUpRanking = includeOnFollowUpRanking;
    FollowUpInterviewDefaultMinutes = followUpInterviewDefaultMinutes;
    DefaultOrder = defaultOrder;
    VIQOrder = viqOrder;
  }

  internal Award ChangeKeyword([NotNull] string keyword)
  {
    SetKeyword(keyword);
    return this;
  }

  private void SetKeyword([NotNull] string keyword)
  {
    Keyword = Check.NotNullOrEmpty(
        keyword,
        nameof(keyword),
        maxLength: AwardConsts.KeywordMaxLength
    );
  }

}
