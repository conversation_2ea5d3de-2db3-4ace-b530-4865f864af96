﻿using RoboMentors.Identity.Permissions;
using RoboMentors.Identity.Users;

namespace RoboMentors.Identity.Roles;

public static class RoboMentorsRoles
{
  public enum SubRoleType
  {
    Manage,
    Viewable
  }
  private static string DefaultPrimaryContact = "<EMAIL>";

  // Event Management Roles
  public static class EventManagement
  {
    // Event Admin
    public static class Admin
    {
      public const string Name = "Event Admin";
      public const string Abbrev = "EAdmin";
      public const int DaysAvailableAfterEvent = 5;

      public static string[] Permissions = new string[]
      {
          //"AbpIdentity.Users",
          //"AbpIdentity.Users.Create",
          //"AbpIdentity.Users.Update",
          RoboMentorsPermissions.Events.Default,
          RoboMentorsPermissions.Events.Create,
          RoboMentorsPermissions.Events.Edit,
          RoboMentorsPermissions.Events.Delete,
          RoboMentorsPermissions.Events.Users,
          //RoboMentorPermissions.EventGroups.Default,
          //RoboMentorPermissions.EventGroups.Create,
          //RoboMentorPermissions.EventGroups.Edit,
          //RoboMentorPermissions.EventGroups.Delete,
          RoboMentorsPermissions.Regions.Default,
          RoboMentorsPermissions.Regions.Create,
          RoboMentorsPermissions.Regions.Edit,
          RoboMentorsPermissions.Regions.Delete,
          RoboMentorsPermissions.Regions.Synchronize,
          //RoboMentorPermissions.Teams.Default,
          //RoboMentorPermissions.Teams.Synchronize,
          RoboMentorsPermissions.Awards.Default,
          //RoboMentorPermissions.Users.Default,
          //RoboMentorPermissions.Users.Create,
          //RoboMentorPermissions.Users.Edit,
          //RoboMentorPermissions.Users.Delete,
          //RoboMentorPermissions.Users.ChangePassword,
          //RoboMentorPermissions.Users.ResetAffiliations,
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.EventManagment.Admin.UserName
      };

      public static string[] ManageRoles = new string[]
      {
        RoboMentorsRoles.EventManagement.Partner.Name,
        RoboMentorsRoles.EventManagement.DivisionManager.Name,
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name
        // Head Ref
			};

      public static string[] ViewableRoles = new string[]
      {
        RoboMentorsRoles.EventManagement.Partner.Name,
        RoboMentorsRoles.EventManagement.DivisionManager.Name,
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name
        // Head Ref
			};
      public static string PrimaryContact = DefaultPrimaryContact;
    }

    // Event Partner
    public static class Partner
    {
      public const string Name = "Event Partner";
      public const string Abbrev = "EP";
      public const int DaysAvailableAfterEvent = 5;

      public static string[] Permissions = new string[]
      {
          //"AbpIdentity.Users",
          //"AbpIdentity.Users.Create",
          //"AbpIdentity.Users.Update",
          RoboMentorsPermissions.Events.Default,
          RoboMentorsPermissions.Events.Edit,
          RoboMentorsPermissions.Events.Users,
          //RoboMentorPermissions.Events.JudgeAssist,
          //RoboMentorPermissions.EventGroups.Default,
          RoboMentorsPermissions.Regions.Default,
          //RoboMentorPermissions.Teams.Default,
          RoboMentorsPermissions.Awards.Default,
          //RoboMentorPermissions.Users.Default,
          //RoboMentorPermissions.Users.Create,
          //RoboMentorPermissions.Users.Edit,
          //RoboMentorPermissions.Users.Delete,
          //RoboMentorPermissions.Users.ChangePassword
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.EventManagment.Partner.UserName
       };

      public static string[] ManageRoles = new string[]
      {
          //RoboMentorRoles.EventManagement.DivisionManager.Name,
          //RoboMentorRoles.Judging.Advisor.Name,          
          //RoboMentorRoles.Judging.DivisionAdvisor.Name, 
          RoboMentorsRoles.Judging.Judge.Name,
          // Head Ref, - Future
          // Ref & other event roles - Future
			};

      public static string[] ViewableRoles = new string[]
      {
        "AllEventRoles"
      };
      public static string PrimaryContact = DefaultPrimaryContact;
    }

    public static class DivisionManager
    {
      public const string Name = "Division Manager";
      public const string Abbrev = "DM";
      public const int DaysAvailableAfterEvent = 1;

      public static string[] Permissions = new string[]
      {
        RoboMentorsPermissions.Events.Default,
        //RoboMentorPermissions.Events.JudgeAssist,
        //RoboMentorPermissions.EventGroups.Default,
        RoboMentorsPermissions.Regions.Default,
        //RoboMentorPermissions.Teams.Default,
        RoboMentorsPermissions.Awards.Default,
      };

      public static string[] Users = new string[]
      {
          RoboMentorsUsers.EventManagment.DivisionManager.UserName
      };

      public static string[] ManageRoles = new string[]
      {
        // Head Ref, - Future
        // Ref & other event roles - Future
      };

      public static string[] ViewableRoles = new string[]
      {
          "AllEventRoles"
      };
      public static string PrimaryContact = DefaultPrimaryContact;
    }

  }

  // Judging Roles
  public static class Judging
  {
    // Judging Admin
    public static class Admin
    {
      public const string Name = "Judging Admin";
      public const string Abbrev = "JAdmin";
      public const int DaysAvailableAfterEvent = 7;

      public static string[] Permissions = new string[]
      {
          RoboMentorsPermissions.Events.Default,
          //RoboMentorsPermissions.Events.Create,
          RoboMentorsPermissions.Events.Edit,
          RoboMentorsPermissions.Events.Users,
          //RoboMentorPermissions.Events.JudgeAssist,
          //RoboMentorsPermissions.Events.Delete,
          RoboMentorsPermissions.Events.Interview,
          //RoboMentorPermissions.EventGroups.Default,
          //RoboMentorPermissions.EventGroups.Create,
          //RoboMentorPermissions.EventGroups.Edit,
          //RoboMentorPermissions.EventGroups.Delete,
          RoboMentorsPermissions.Regions.Default,
          RoboMentorsPermissions.Regions.Create,
          RoboMentorsPermissions.Regions.Edit,
          RoboMentorsPermissions.Regions.Delete,
          RoboMentorsPermissions.Regions.Synchronize,
          //RoboMentorPermissions.Teams.Default,
          //RoboMentorPermissions.Teams.Synchronize,
          RoboMentorsPermissions.Awards.Default,
          RoboMentorsPermissions.Awards.Create,
          RoboMentorsPermissions.Awards.Edit,
          RoboMentorsPermissions.Awards.Delete,
          RoboMentorsPermissions.Questions.Default,
          RoboMentorsPermissions.Questions.Create,
          RoboMentorsPermissions.Questions.Edit,
          RoboMentorsPermissions.Questions.Delete,
          //RoboMentorPermissions.Imports.Default,
          //RoboMentorPermissions.Imports.CreateSetting,
          //RoboMentorPermissions.Imports.EditSetting,
          //RoboMentorPermissions.Imports.DeleteSetting,
          //RoboMentorPermissions.Imports.SyncRobotEvents,
          //RoboMentorPermissions.Imports.SyncGoogleSheets,
          //RoboMentorPermissions.Users.Default,
          //RoboMentorPermissions.Users.Create,
          //RoboMentorPermissions.Users.Edit,
          //RoboMentorPermissions.Users.Delete,
          //RoboMentorPermissions.Users.ChangePassword,
          //RoboMentorPermissions.Users.ResetAffiliations,
          //"AbpIdentity.Users",
          //"AbpIdentity.Users.Create",
          //"AbpIdentity.Users.Update"
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Judging.Admin.UserName,
        RoboMentorsUsers.Automation.Service.UserName
      };

      public static string[] ManageRoles = new string[]
      {
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name,
        RoboMentorsRoles.Judging.DisabledJudge.Name,
      };

      public static string[] ViewableRoles = new string[]
      {
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name
      };
      public static string PrimaryContact = DefaultPrimaryContact;
    }

    // Judging Advisor
    public static class Advisor
    {
      public const string Name = "Judge Advisor";
      public const string Abbrev = "JA";
      public const int DaysAvailableAfterEvent = 3;

      public static string[] Permissions = new string[]
      {
        RoboMentorsPermissions.Events.Default,
        RoboMentorsPermissions.Events.Edit,
        RoboMentorsPermissions.Events.Users,
        RoboMentorsPermissions.Events.Interview,
        //RoboMentorPermissions.Events.JudgeAssist,
        //RoboMentorPermissions.EventGroups.Default,
        RoboMentorsPermissions.Regions.Default,
        //RoboMentorPermissions.Teams.Default,
        RoboMentorsPermissions.Awards.Default,
        RoboMentorsPermissions.Questions.Default,
        //RoboMentorPermissions.Users.Default,
        //RoboMentorPermissions.Users.Create,
        //RoboMentorPermissions.Users.Edit,
        //RoboMentorPermissions.Users.ChangePassword,
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Judging.Advisor.UserName
      };

      public static string[] ManageRoles = new string[]
      {
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name
      };

      public static string[] ViewableRoles = new string[]
      {
        RoboMentorsRoles.EventManagement.Partner.Name,
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name
      };
      public static string PrimaryContact = RoboMentorsRoles.EventManagement.Partner.Name;
    }

    // Judging Division Advisor
    public static class DivisionAdvisor
    {
      public const string Name = "Division Judge Advisor";
      public const string Abbrev = "Div JA";
      public const int DaysAvailableAfterEvent = 1;

      public static string[] Permissions = new string[]
      {
        RoboMentorsPermissions.Events.Default,
        RoboMentorsPermissions.Events.Edit,
        RoboMentorsPermissions.Events.Users,
        RoboMentorsPermissions.Events.Interview,
        //RoboMentorPermissions.Events.JudgeAssist,
        //RoboMentorPermissions.EventGroups.Default,
        RoboMentorsPermissions.Regions.Default,
        //RoboMentorPermissions.Teams.Default,
        RoboMentorsPermissions.Awards.Default,
        RoboMentorsPermissions.Questions.Default,
        //RoboMentorPermissions.Users.Default,
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Judging.DivisionAdvisor.UserName
      };

      public static string[] ManageRoles = new string[]
      {
        RoboMentorsRoles.Judging.Judge.Name
      };

      public static string[] ViewableRoles = new string[]
      {
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
        RoboMentorsRoles.Judging.Judge.Name
      };
      public static string PrimaryContact = RoboMentorsRoles.Judging.Advisor.Name;
    }

    // Judging Judge
    public static class Judge
    {
      public const string Name = "Judge";
      public const string Abbrev = "Jdg";
      public const int DaysAvailableAfterEvent = 0;

      public static string[] Permissions = new string[]
      {
        RoboMentorsPermissions.Events.Default,
        RoboMentorsPermissions.Events.Interview,
        RoboMentorsPermissions.Regions.Default,
        //RoboMentorPermissions.Teams.Default,
        RoboMentorsPermissions.Awards.Default,
        RoboMentorsPermissions.Questions.Default
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Judging.Judge.UserName
      };

      public static string[] ManageRoles = new string[]
      {
      };

      public static string[] ViewableRoles = new string[]
      {
        RoboMentorsRoles.EventManagement.Partner.Name,
        RoboMentorsRoles.Judging.Advisor.Name,
        RoboMentorsRoles.Judging.DivisionAdvisor.Name,
      };
      public static string PrimaryContact = RoboMentorsRoles.Judging.Advisor.Name;
    }

    // Judging Disabled Judge
    public static class DisabledJudge
    {
      public const string Name = "Disabled Judge";
      public const string Abbrev = "DisabledJdg";
      public const int DaysAvailableAfterEvent = 0;

      public static string[] Permissions = new string[]
      {
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Judging.DisabledJudge.UserName
      };

      public static string[] ManageRoles = new string[]
      {
      };

      public static string[] ViewableRoles = new string[]
      {
      };
      public static string PrimaryContact = "<EMAIL>";
    }

  }

  // Automation Roles
  public static class Automation
  {
    // Service 
    public static class Service
    {
      public static string Name = "Service";
      public const string Abbrev = "AutoSrv";
      public const int DaysAvailableAfterEvent = 365;

      public static string[] Permissions = new string[]
      {
        //RoboMentorPermissions.Services.Execute,
        RoboMentorsPermissions.Events.Default,
        //RoboMentorPermissions.Imports.SyncGoogleSheets
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Automation.Service.UserName
      };

      public static string[] ManageRoles = new string[]
      {
      };

      public static string[] ViewableRoles = new string[]
      {
      };
      public static string PrimaryContact = DefaultPrimaryContact;
    }
  }



  // Training Roles
  public static class Training
  {
    // Training Admin
    public static class Admin
    {
      public const string Name = "Training Admin";
      public const string Abbrev = "TAdmin";
      public const int DaysAvailableAfterEvent = 0;

      public static string[] Permissions = new string[]
      {
        "AbpIdentity.Users",
        "AbpIdentity.Users.Create",
        "AbpIdentity.Users.Update"
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Training.Admin.UserName
      };

      public static string[] ManageRoles = new string[]
      {
        RoboMentorsRoles.Training.Author.Name
      };

      public static string[] ViewableRoles = new string[]
      {
        RoboMentorsRoles.Training.Author.Name
      };
    }

    // Training Author
    public static class Author
    {
      public const string Name = "Author";
      public const string Abbrev = "Auth";
      public const int DaysAvailableAfterEvent = 0;

      public static string[] Permissions = new string[]
      {
      };

      public static string[] Users = new string[]
      {
        RoboMentorsUsers.Training.Author.UserName
      };

      public static string[] ManageRoles = new string[]
      {
      };

      public static string[] ViewableRoles = new string[]
      {
      };
    }
  }
}
