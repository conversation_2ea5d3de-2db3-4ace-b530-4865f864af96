using Microsoft.EntityFrameworkCore;
using RoboMentors.EventManagement.Regions;
using RoboMentors.EventManagement.Teams;
using RoboMentors.Judging.Awards;
using RoboMentors.Judging.Questions;
using Volo.Abp.AuditLogging.EntityFrameworkCore;
using Volo.Abp.BackgroundJobs.EntityFrameworkCore;
using Volo.Abp.BlobStoring.Database.EntityFrameworkCore;
using Volo.Abp.Data;
using Volo.Abp.DependencyInjection;
using Volo.Abp.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore.Modeling;
using Volo.Abp.FeatureManagement.EntityFrameworkCore;
using Volo.Abp.Identity;
using Volo.Abp.Identity.EntityFrameworkCore;
using Volo.Abp.OpenIddict.EntityFrameworkCore;
using Volo.Abp.PermissionManagement.EntityFrameworkCore;
using Volo.Abp.SettingManagement.EntityFrameworkCore;

namespace RoboMentors.EntityFrameworkCore;

[ReplaceDbContext(typeof(IIdentityDbContext))]
[ConnectionStringName("Default")]
public class RoboMentorsDbContext :
    AbpDbContext<RoboMentorsDbContext>,
    IIdentityDbContext
{
  /* Add DbSet properties for your Aggregate Roots / Entities here. */

  // Event Management Entities
  //public DbSet<Event> Events { get; set; }
  //public DbSet<EventAward> EventAwards { get; set; }
  //public DbSet<EventAwardDivisionWinner> EventAwardDivisionWinners { get; set; }
  //public DbSet<EventDivision> EventDivisions { get; set; }
  //public DbSet<EventMatch> EventMatches { get; set; }
  //public DbSet<EventPlanningDivision> EventPlanningDivisions { get; set; }
  //public DbSet<EventRanking> EventRankings { get; set; }
  //public DbSet<EventSkill> EventSkills { get; set; }
  //public DbSet<EventTeam> EventTeams { get; set; }
  //public DbSet<EventUser> EventUsers { get; set; }
  //public DbSet<Picture> Pictures { get; set; }
  public DbSet<Region> Regions { get; set; }
  public DbSet<Team> Teams { get; set; }

  //
  //// Judging Entities
  //public DbSet<AwardRanking> AwardRankings { get; set; }
  public DbSet<Award> Awards { get; set; }
  //public DbSet<ENRubric> ENRubrics { get; set; }
  //public DbSet<Interview> Interviews { get; set; }
  public DbSet<Question> Questions { get; set; }
  //
  //// Training Entities


  #region Entities from the modules

  /* Notice: We only implemented IIdentityProDbContext 
   * and replaced them for this DbContext. This allows you to perform JOIN
   * queries for the entities of these modules over the repositories easily. You
   * typically don't need that for other modules. But, if you need, you can
   * implement the DbContext interface of the needed module and use ReplaceDbContext
   * attribute just like IIdentityProDbContext .
   *
   * More info: Replacing a DbContext of a module ensures that the related module
   * uses this DbContext on runtime. Otherwise, it will use its own DbContext class.
   */

  // Identity
  public DbSet<IdentityUser> Users { get; set; }
  public DbSet<IdentityRole> Roles { get; set; }
  public DbSet<IdentityClaimType> ClaimTypes { get; set; }
  public DbSet<OrganizationUnit> OrganizationUnits { get; set; }
  public DbSet<IdentitySecurityLog> SecurityLogs { get; set; }
  public DbSet<IdentityLinkUser> LinkUsers { get; set; }
  public DbSet<IdentityUserDelegation> UserDelegations { get; set; }
  public DbSet<IdentitySession> Sessions { get; set; }

    #endregion

  public RoboMentorsDbContext(DbContextOptions<RoboMentorsDbContext> options)
      : base(options)
  {

  }

  protected override void OnModelCreating(ModelBuilder builder)
  {
    base.OnModelCreating(builder);

    /* Include modules to your migration db context */

    builder.ConfigurePermissionManagement();
    builder.ConfigureSettingManagement();
    builder.ConfigureBackgroundJobs();
    builder.ConfigureAuditLogging();
    builder.ConfigureFeatureManagement();
    builder.ConfigureIdentity();
    builder.ConfigureOpenIddict();
    builder.ConfigureBlobStoring();

    /* Configure your own tables/entities inside here */

    //builder.Entity<YourEntity>(b =>
    //{
    //    b.ToTable(RoboMentorsConsts.DbTablePrefix + "YourEntities", RoboMentorsConsts.DbSchema);
    //    b.ConfigureByConvention(); //auto configure for the base class props
    //    //...
    //});

    ////******************************************************************************************
    //// Event Management Entities
    ////******************************************************************************************
    ////* - Events
    //builder.Entity<Event>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "Events",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.Property(x => x.Id).ValueGeneratedNever(); // - add in when recreating table
    //                                               // Collections - General
    //  e.HasMany(x => x.Awards).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.AwardDivisionWinners).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Divisions).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Matches).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.PlanningDivisions).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Pictures).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Rankings).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Skills).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Teams).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Users).WithOne().HasForeignKey(x => x.EventId);
    //  // Collections - Judging
    //  e.HasMany(x => x.AwardRankings).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.ENRubrics).WithOne().HasForeignKey(x => x.EventId);
    //  e.HasMany(x => x.Interviews).WithOne().HasForeignKey(x => x.EventId);
    //});
    //
    ////* - Event Awards
    //builder.Entity<EventAward>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventAwards",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.Id });
    //});
    //
    ////* - Event Award Division Winners
    //builder.Entity<EventAwardDivisionWinner>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventAwardDivisionWinners",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.Id, e.DivisionId });
    //});
    //
    ////* - Event Divisions
    //builder.Entity<EventDivision>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventDivisions",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.DivisionId });
    //});
    //
    ////* - Event Matches
    //builder.Entity<EventMatch>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventMatches",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.Id });
    //});
    //
    ////* - Event Judging Divisions
    //builder.Entity<EventPlanningDivision>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventPlanningDivisions",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.PlanningDivisionId });
    //});
    //
    ////* - Event Rankings
    //builder.Entity<EventRanking>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventRankings",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.DivisionId, e.Rank });
    //});
    //
    ////* - Event Skills
    //builder.Entity<EventSkill>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventSkills",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.TeamNumber });
    //});
    //
    ////* - Event Teams
    //builder.Entity<EventTeam>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventTeams",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.Number });
    //});
    //
    ////* - Event Users
    //builder.Entity<EventUser>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "EventUsers",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.EventId, e.UserId });
    //});
    //
    ////* - Pictures
    //builder.Entity<Picture>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.EventManagementDbTablePrefix + "Pictures",
    //            RoboMentorsV3Consts.EventManagementDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasKey(e => new { e.Id });
    //  e.HasIndex(x => new { x.EventId, x.TeamNumber }, "IX_Picture_Event_TeamNumber");
    //});
    //
    //* - Region
    builder.Entity<Region>(e =>
    {
      e.ToTable(RoboMentorsConsts.EventManagementDbTablePrefix + "Regions",
                RoboMentorsConsts.EventManagementDbSchema);
      e.ConfigureByConvention();
      e.HasKey(e => new { e.Id });
      e.HasIndex(x => new { x.Country, x.RegionName }, "IX_Region_Country_RegionName").IsUnique();
      e.Property(x => x.Country).HasMaxLength(RegionConsts.CountryMaxLength);
      e.Property(x => x.RegionName).HasMaxLength(RegionConsts.RegionNameMaxLength);
    });

    //* - Team
    builder.Entity<Team>(e =>
    {
      e.ToTable(RoboMentorsConsts.EventManagementDbTablePrefix + "Teams",
                RoboMentorsConsts.EventManagementDbSchema);
      e.ConfigureByConvention();
      e.HasKey(e => new { e.Id });
    
      //Configure the Id to be manually set instead of auto-generated
      e.Property(e => e.Id).ValueGeneratedNever();
    
      e.HasIndex(x => new { x.ProgramText, x.Number }, "IX_Team_Program_Number").IsUnique();
      e.HasIndex(x => new { x.Number }, "IX_Team_Number");
    });


    ////******************************************************************************************
    //// Judging Entities
    ////******************************************************************************************
    ////* - Judging Event Collections
    ////******************************************************************************************
    ////* -- Award Rankings
    //builder.Entity<AwardRanking>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.JudgingDbTablePrefix + "AwardRankings",
    //            RoboMentorsV3Consts.JudgingDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasIndex(x => new { x.EventId, x.PlanningDivisionId, x.InterviewType, x.InterviewTeam, x.EventAwardId, x.TeamNumber }, "IX_AwardRanking_Event_PlanningDivision_InterviewType_InterviewTeam_EventAward_TeamNumber");
    //});
    //
    ////* -- Engineering Notebook Rubrics
    //builder.Entity<ENRubric>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.JudgingDbTablePrefix + "ENRubrics",
    //            RoboMentorsV3Consts.JudgingDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasIndex(x => new { x.EventId, x.PlanningDivisionId, x.ENTeam, x.TeamNumber }, "IX_ENRubric_Event_PlanningDivision_ENTeam_TeamNumber");
    //});
    //
    ////* -- Interviews
    //builder.Entity<Interview>(e =>
    //{
    //  e.ToTable(RoboMentorsV3Consts.JudgingDbTablePrefix + "Interviews",
    //            RoboMentorsV3Consts.JudgingDbSchema);
    //  e.ConfigureByConvention();
    //  e.HasIndex(x => new { x.EventId, x.PlanningDivisionId, x.Type, x.InterviewTeam, x.TeamNumber }, "IX_Interview_Event_PlanningDivision_Type_InterviewTeam_TeamNumber");
    //});
    //
    //******************************************************************************************
    //* - Judging Maintenance
    //******************************************************************************************
    //* -- Awards
    builder.Entity<Award>(e =>
    {
      e.ToTable(RoboMentorsConsts.JudgingDbTablePrefix + "Awards",
                RoboMentorsConsts.JudgingDbSchema);
      e.ConfigureByConvention();
      e.Property(x => x.Id).ValueGeneratedNever();
      e.HasIndex(x => x.Keyword);
    });
    
    //* -- Questions
    builder.Entity<Question>(e =>
    {
      e.ToTable(RoboMentorsConsts.JudgingDbTablePrefix + "Questions",
                RoboMentorsConsts.JudgingDbSchema);
      e.ConfigureByConvention();
    });
    
    ////******************************************************************************************
    //// Training Entities
    ////******************************************************************************************

  }
}
