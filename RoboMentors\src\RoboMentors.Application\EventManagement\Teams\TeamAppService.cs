﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RoboMentors.EventManagement.RobotEvents;
using RoboMentors.Extensions;
using RoboMentors.Identity.Permissions;
using RoboMentors.Managers.Apis.RobotEvents;
using RoboMentors.Utilities;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.BackgroundJobs;

namespace RoboMentors.EventManagement.Teams;

[Authorize(RoboMentorsPermissions.Teams.Default)]
public class TeamsAppService : RoboMentorsAppService, ITeamAppService
{
  // Repositories
  private readonly ITeamRepository _teamRepository;
  private readonly IRobotEventsTeamManager _robotEventsTeamManager;

  // Services
  private readonly IBackgroundJobManager _backgroundJobManager;
  //TelemetryClient _telemetryClient;


  public TeamsAppService(
      ITeamRepository teamRepository,
      IRobotEventsTeamManager robotEventsTeamManager,
      IBackgroundJobManager backgroundJobManager)
      //TelemetryClient telemetryClient)
  {
    _teamRepository = teamRepository;
    _robotEventsTeamManager = robotEventsTeamManager;
    _backgroundJobManager = backgroundJobManager;
    //_telemetryClient = telemetryClient;
  }

  // Static dictionary to store job results (temporary for demo)
  internal static readonly Dictionary<string, SyncResult> JobResults = new Dictionary<string, SyncResult>();

  [ApiExplorerSettings(IgnoreApi = true)]
  public async Task<List<TeamDto>> GetTeamsAsync()
  {
    var teams = await _teamRepository.GetListAsync().ConfigureAwait(false);
    return ObjectMapper.Map<List<Team>, List<TeamDto>>(teams);
  }

  [ApiExplorerSettings(IgnoreApi = true)]
  public async Task<List<TeamDto>> GetTeamsThatStartsWithAsync(string teamNumber, int limit = 200)
  {
    var teams = await _teamRepository.GetTeamsThatStartWithAsync(teamNumber, limit).ConfigureAwait(false);
    return ObjectMapper.Map<List<Team>, List<TeamDto>>(teams);
  }

  [ApiExplorerSettings(IgnoreApi = true)]
  public async Task<List<TeamDto>> GetTeamsAsync(EventProgram program)
  {
    var teams = await _teamRepository.FindByProgramAsync(program).ConfigureAwait(false);
    return ObjectMapper.Map<List<Team>, List<TeamDto>>(teams);
  }

  [ApiExplorerSettings(IgnoreApi = true)]
  public async Task<Dictionary<string, string>> GetProgramTeamNumbersAsync(EventProgram? program = null)
  {
    return await _teamRepository.GetProgramTeamNumberRegionsAsync(program).ConfigureAwait(false);
  }

  [ApiExplorerSettings(IgnoreApi = true)]
  public async Task<TeamDto?> FindTeamByProgramTeamNumberAsync(EventProgram program, string teamNumber)
  {
    var team = await _teamRepository.FindByProgramTeamNumberAsync(program, teamNumber).ConfigureAwait(false);
    return ObjectMapper.Map<Team, TeamDto>(team);
  }

  [Authorize(RoboMentorsPermissions.Teams.Synchronize)]
  public async Task<SyncResult> RequestSyncTeamsAsync(List<EventProgram>? programs = null)
  {
    return await SyncRETeamsAsync(programs).ConfigureAwait(false);
  }

  //[ApiExplorerSettings(IgnoreApi = true)]
  [Authorize(RoboMentorsPermissions.Services.Execute)]
  public async Task<SyncJobResponse> SyncTeamsAsync()
  {
    var jobId = Guid.NewGuid().ToString();
    try
    {
      //_telemetryClient.TrackTrace($"TeamsAppService:SyncTeamsAsync: Starting with job id {jobId}");
      //await _backgroundJobManager.EnqueueAsync<SyncTeamsBackgroundJobArgs>(new SyncTeamsBackgroundJobArgs { JobId = jobId });
      //_telemetryClient.TrackTrace($"TeamsAppService:SyncTeamsAsync: Enqueued request");
      return new SyncJobResponse
      {
        JobId = jobId,
        Status = "Queued",
        Message = "Team synchronization job has been queued."
      };
    }
    catch (Exception ex)
    {
      //var telemetry = new ExceptionTelemetry(new InvalidOperationException($"TeamsAppService:SyncTeamsAsync: Exception thrown = {ex}", ex));
      //_telemetryClient.TrackException(telemetry);
      return new SyncJobResponse
      {
        JobId = jobId,
        Status = "Not Queued: Exception thrown",
        Message = $"Exception = {ex}"
      };
    }
  }

  //[ApiExplorerSettings(IgnoreApi = true)]
  [Authorize(RoboMentorsPermissions.Services.Execute)]
  public Task<SyncResult> GetSyncTeamsStatusAsync(string jobId)
  {
    //_telemetryClient.TrackTrace($"TeamsAppService:GetSyncTeamsStatusAsync: Starting with job id {jobId}.");
    if (JobResults.TryGetValue(jobId, out var result))
    {
      //_telemetryClient.TrackTrace($"TeamsAppService:GetSyncTeamsStatusAsync: Found job id {jobId}. Returning result {result.Log}");
      return Task.FromResult(result);
    }
    var newResult = new SyncResult { Success = false, Log = $"Job '{jobId}' is still in progress or not found." };
    //_telemetryClient.TrackTrace($"TeamsAppService:GetSyncTeamsStatusAsync: Did NOT find job id {jobId}. Returning result {newResult.Log}");

    return Task.FromResult(newResult);
  }

  // Should there be permissions on this as called from Background Job?
  //[ApiExplorerSettings(IgnoreApi = true)]
  //[Authorize(RoboMentorsPermissions.Services.Execute)]
  public async Task<SyncResult> SyncRETeamsAsync(List<EventProgram>? programs = null)
  {
    return await SyncService.ExecuteAsync(async (results, log) =>
    {
      programs = programs ?? new List<EventProgram>() { EventProgram.VURC, EventProgram.V5RC, EventProgram.VIQRC, EventProgram.VAIRC };
      foreach (var program in programs)
      {
        var programIds = new List<int>() { (int)program };
        log.WriteStart($"Retrieving RE teams in program: '{program} ({programIds.Select(x => x.ToString()).ToList().ToDelimitedString()})'. ");
        var reTeams = await _robotEventsTeamManager.GetTeamsAsync(true, programIds).ConfigureAwait(false);
        log.WriteEnd($"Done. Retrieved {reTeams.Count:N0} teams");

        // Get the list of teams from the database
        log.WriteLine();
        log.WriteStart($"Retrieving DB teams in program: '{program}'. ");
        var dbTeams = await _teamRepository.FindByProgramAsync(program).ConfigureAwait(false);
        log.WriteEnd($"Done. Retrieved {dbTeams.Count:N0} teams");

        // Delete missing records
        log.WriteLine();
        log.WriteLine($"Deleting {program} teams. ");
        var rePgmTeams = reTeams.Select(t => $"{t.Program.Code}-{t.Number}").ToList();
        var deleteItems = dbTeams.Where(t => rePgmTeams.Contains($"{t.Program.ToString()}-{t.Number}") == false).ToList();
        foreach (var deleteItem in deleteItems)
        {
          log.WriteLine($"Delete team '{deleteItem.Program.ToString()}: {deleteItem.Number} - {deleteItem.Name}'");
        }
        if (deleteItems.Count > 0)
        {
          log.WriteStart($"Deleting {deleteItems.Count:N0} missing {program} teams. ");
          await _teamRepository.DeleteManyAsync(deleteItems, true).ConfigureAwait(false);
          log.WriteEnd($"Done.");
        }
        else
        {
          log.WriteLine($"No missing {program} teams to delete.");
        }

        // Update existing records
        var dbTeamsToUpdate = dbTeams.Where(t => rePgmTeams.Contains($"{t.Program.ToString()}-{t.Number}") == true).ToList();
        log.WriteLine();
        log.WriteLine($"Possibly updating {dbTeamsToUpdate.Count:N0} {program} teams. ");
        var updateItems = new List<Team>();
        foreach (var dbTeam in dbTeamsToUpdate.OrderBy(x => x.Program).ThenBy(x => x.Number))
        {
          var newTeam = reTeams.FirstOrDefault(t => $"{t.Program.Code}-{t.Number}" == $"{dbTeam.Program.ToString()}-{dbTeam.Number}");
          if (newTeam != null)
          {
            if (dbTeam.Id != newTeam.Id)
            {
              log.WriteLine($"Update team's id: '{dbTeam.Id} to {newTeam.Id} for {dbTeam.Program.ToString()}-{dbTeam.Number} - {dbTeam.Name}'");
              await _teamRepository.UpdateAsync(dbTeam, true).ConfigureAwait(false);
              dbTeam.Id = newTeam.Id;
              await _teamRepository.InsertAsync(dbTeam, true).ConfigureAwait(false);

            } else if (dbTeam.Number != newTeam.Number ||
                dbTeam.Name != newTeam.Team_name ||
                dbTeam.RobotName != newTeam.Robot_name ||
                dbTeam.Organization != newTeam.Organization ||
                dbTeam.City != newTeam.Location.City ||
                dbTeam.Region != newTeam.Location.Region ||
                dbTeam.Country != newTeam.Location.Country ||
                dbTeam.Grade != newTeam.Grade ||
                dbTeam.Program != newTeam.Program.Code.ToEventProgram() ||
                dbTeam.ProgramText != newTeam.Program.Code ||
                dbTeam.Latitude != newTeam.Location.Coordinates.Lat ||
                dbTeam.Longitude != newTeam.Location.Coordinates.Lon)
            {
              log.WriteLine($"Update team '{dbTeam.Program.ToString()}-{dbTeam.Number} - {dbTeam.Name}'");
              dbTeam.Number = newTeam.Number;
              dbTeam.Name = newTeam.Team_name;
              dbTeam.RobotName = newTeam.Robot_name;
              dbTeam.Organization = newTeam.Organization;
              dbTeam.City = newTeam.Location.City;
              dbTeam.Region = newTeam.Location.Region;
              dbTeam.Country = newTeam.Location.Country;
              dbTeam.Grade = newTeam.Grade;
              dbTeam.Program = newTeam.Program.Code.ToEventProgram();
              dbTeam.ProgramText = newTeam.Program.Code;
              dbTeam.Latitude = newTeam.Location.Coordinates.Lat;
              dbTeam.Longitude = newTeam.Location.Coordinates.Lon;
              updateItems.Add(dbTeam);
            }
          }
          else
          {
            log.WriteLine($"Team not found '{dbTeam.Program.ToString()}-{dbTeam.Number} - {dbTeam.Name}'");
          }
        }
        if (updateItems.Count > 0)
        {
          log.WriteStart($"Updating {updateItems.Count:N0} {program} teams. ");
          await _teamRepository.UpdateManyAsync(updateItems, true).ConfigureAwait(false);
          log.WriteEnd($"Done.");
        }
        else
        {
          log.WriteLine($"No {program} teams to update. ");
        }

        // Add new records
        log.WriteLine();
        log.WriteLine($"Adding {program} teams. ");
        var currentPgmTeams = dbTeams.Select(t => $"{t.Program.ToString()}-{t.Number}").ToList();
        var newItems = reTeams.Where(t => currentPgmTeams.Contains($"{t.Program.Code}-{t.Number}") == false).ToList();
        var insertItems = new List<Team>();
        foreach (var newItem in newItems.OrderBy(x => x.Program.Code).ThenBy(x => x.Number))
        {
          log.WriteLine($"Add team '{newItem.Program.Code}-{newItem.Number} - {newItem.Team_name}'");
          insertItems.Add(new Team(
            id: newItem.Id,
            number: newItem.Number,
            name: newItem.Team_name,
            robotName: newItem.Robot_name,
            organization: newItem.Organization,
            city: newItem.Location.City,
            region: newItem.Location.Region,
            country: newItem.Location.Country,
            grade: newItem.Grade,
            program: newItem.Program.Code.ToEventProgram(),
            programText: newItem.Program.Code,
            latitude: newItem.Location.Coordinates.Lat,
            longitude: newItem.Location.Coordinates.Lon
          ));
        }
        if (insertItems.Count > 0)
        {
          log.WriteStart($"Adding {insertItems.Count:N0} new {program} teams. ");
          await _teamRepository.InsertManyAsync(insertItems, true).ConfigureAwait(false);
          log.WriteEnd($"Done.");
        }
        else
        {
          log.WriteLine($"No new {program} teams to add");
        }

        log.WriteLine($"Team synchronization completed successfully for Program: {program.ToString()}");
      }

      log.WriteLine($"Team synchronization completed successfully!");
      results.Success = true;
    }).ConfigureAwait(false);
  }
}


//public class SyncTeamsBackgroundJob : AsyncBackgroundJob<SyncTeamsBackgroundJobArgs>
//{
//  private readonly TeamsAppService _teamsAppService;
//  //private TelemetryClient _telemtryClient;
//
//  public SyncTeamsBackgroundJob(TeamsAppService teamsAppService)
//                                //TelemetryClient telemetryClient)
//  {
//    _teamsAppService = teamsAppService;
//    //_telemtryClient = telemetryClient;
//  }
//
//  public override async Task ExecuteAsync(SyncTeamsBackgroundJobArgs args)
//  {
//    try
//    {
//      //_telemtryClient.TrackTrace($"SyncTeamsBackgroundJob:ExecuteAsync: Calling SyncRETeamsAsync");
//      var sw = new Stopwatch();
//      sw.Start();
//      var result = await _teamsAppService.SyncRETeamsAsync();
//      //_telemtryClient.TrackTrace($"SyncTeamsBackgroundJob:ExecuteAsync: SyncRETeamsAsync done Duration = {sw.Elapsed.TotalSeconds:0.0} secs");
//
//      TeamsAppService.JobResults[args.JobId] = result; // Store result
//      //_telemtryClient.TrackTrace($"SyncTeamsBackgroundJob:ExecuteAsync: Results saved. Log = {result.Log}");
//    }
//    catch (Exception ex)
//    {
//      //var telemetry = new ExceptionTelemetry(new InvalidOperationException($"SyncTeamsBackgroundJob:ExecuteAsync: Exception thrown = {ex}", ex));
//      //_telemtryClient.TrackException(telemetry);
//    }
//  }
//}
//
//public class SyncTeamsBackgroundJobArgs
//{
//  public string JobId { get; set; }
//}
