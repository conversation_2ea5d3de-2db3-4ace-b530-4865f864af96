﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RoboMentors.EventManagement.RobotEvents;

public static class REExtensions
{
  public static string ToRobotEventTime(this DateTime datetime)
  {
    return datetime.ToString("yyyy-MM-ddTHH:mm:sszzz");
  }

  public static string ToProgramColor(this EventProgram program)
  {
    return
        program == EventProgram.VIQRC ? "primary " :
        program == EventProgram.V5RC ? "danger " :
        program == EventProgram.VURC ? "dark" : "warning";
  }

  // Grade
  public static string ToBorderType(this Grade grade)
  {
    return
        grade == Grade.Elementary_School ? "solid " :
        grade == Grade.Middle_School ? "dashed " :
        grade == Grade.College ? "double " : "solid ";
  }
}
