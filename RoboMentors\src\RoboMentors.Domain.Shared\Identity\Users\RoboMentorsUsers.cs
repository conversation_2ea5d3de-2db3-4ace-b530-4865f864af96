﻿namespace RoboMentors.Identity.Users;

public static class RoboMentorsUsers
{
  public static class EventManagment
  {
    public static class Admin
    {
      public static string UserName = "EAdmin";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "EAdmin";
      public static string Surname = "EManager";
    }
    
    public static class Partner
    {
      public static string UserName = "EPart";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "EP";
      public static string Surname = "EManager";
    }
    
    public static class DivisionManager
    {
      public static string UserName = "DivMgr";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "DM";
      public static string Surname = "DivManager";
    }

  }

  public static class Judging
  {
    public static class Admin
    {
      public static string UserName = "JAdmin";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "JAdmin";
      public static string Surname = "Judge";
    }

    public static class Advisor
    {
      public static string UserName = "JAdvisor";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "JA";
      public static string Surname = "Judge";
    }

    public static class DivisionAdvisor
    {
      public static string UserName = "JDivAdvisor";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "DivJA";
      public static string Surname = "Judge";
    }

    public static class Judge
    {
      public static string UserName = "JJudge";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "JJ";
      public static string Surname = "Judge";
    }

    public static class DisabledJudge
    {
      public static string UserName = "DisabledJudge";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "Disabled";
      public static string Surname = "Judge";
    }

  }

  public static class Automation
  {
    public static class Service
    {
      public static string UserName = "RMService";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "RM";
      public static string Surname = "Service";
    }
  }

  public static class Training
  {
    public static class Admin
    {
      public static string UserName = "TAdmin";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "Admin";
      public static string Surname = "Training";
    }

    public static class Author
    {
      public static string UserName = "TAuthor";
      public static string Email = "<EMAIL>";
      public static string Password = "1q2w3E*";
      public static string Name = "Author";
      public static string Surname = "Training";
    }
  }
}
