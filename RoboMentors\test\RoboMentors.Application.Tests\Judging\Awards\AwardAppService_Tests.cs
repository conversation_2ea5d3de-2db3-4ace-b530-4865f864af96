﻿using RoboMentors.EventManagement.EventAwards;
using RoboMentors.EventManagement.RobotEvents;
using Shouldly;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace RoboMentors.Judging.Awards;

public abstract class AwardAppService_Tests<TStartupModule> : RoboMentorsApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
  private readonly IAwardAppService _awardAppService;
  private readonly TestAppService _testAppService;

  protected AwardAppService_Tests()
  {
    _awardAppService = GetRequiredService<IAwardAppService>();
    _testAppService = GetRequiredService<TestAppService>();
  }

  [Fact]
  public async Task Should_Get_Award()
  {
    // Act
    var award = await _awardAppService.GetDtoAsync(1);

    // Assert
    award.ShouldNotBeNull();
    award.Keyword.ShouldBe("Excellence");
  }

  [Fact]
  public async Task Should_Get_List_Of_Awards()
  {
    // Act
    var results = await _awardAppService.GetDtosAsync();

    // Assert
    results.Count.ShouldBeGreaterThan(0);
    results.ShouldContain(a => a.Keyword == "Excellence");
    results.ShouldContain(a => a.Keyword == "Design");
  }

  [Fact]
  public async Task Should_Get_Filtered_Awards()
  {
    var results = await _awardAppService.GetPagedListAsync(
        new GetListDto { Filter = "Ex" });

    results.TotalCount.ShouldBeGreaterThanOrEqualTo(1);
    results.Items.ShouldContain(award => award.Keyword == "Excellence");
    results.Items.ShouldNotContain(award => award.Keyword == "Design");
  }

  [Fact]
  public async Task Should_Get_List_Of_AwardKeywords()
  {
    // Act
    var result = await _awardAppService.GetKeywordLookupAsync();

    // Assert
    result.Count.ShouldBeGreaterThan(0);
    result.ShouldContain(x => x.Keyword == "Excellence");
  }

  [Fact]
  public async Task Should_Create_A_Valid_Award()
  {
    // Act
    var result = await _awardAppService.CreateAsync(
        new CreateUpdateAwardDto
        {
          Id = 999,
          Keyword = "TestAdd",
          Abbrev = "TA",
          Title = "Test Award",
          Program = EventProgram.All,
          Type = AwardType.Judged,
          Description = "Test Award description",
          Script = "Test Award script",
          Icon = "Test Award icon url",
          IncludeOnInitialRanking = false,
          IncludeOnFollowUpRanking = false,
          FollowUpInterviewDefaultMinutes = AwardConsts.FollowUpInterviewMinutesDefault,
          DefaultOrder = AwardConsts.OrderMax,
          VIQOrder = AwardConsts.OrderMax
        }
    );

    // Assert
    result.Success.ShouldBeTrue();
    result.Data?.Id.ShouldNotBe(0);
    result.Data?.Keyword.ShouldBe("TestAdd");
  }

  [Fact]
  public async Task Should_Not_Create_Award_With_NegativeId()
  {
    // Act
    var result = await _awardAppService.CreateAsync(
        new CreateUpdateAwardDto
        {
          Id = -1,
          Keyword = "TestNegativeId",
          Abbrev = "TNI",
          Title = "Test Negative Id",
          Program = EventProgram.All,
          Type = AwardType.Judged,
          Description = "Test Award description",
          Script = "Test Award script",
          Icon = "Test Award icon url",
          IncludeOnInitialRanking = false,
          IncludeOnFollowUpRanking = false,
          FollowUpInterviewDefaultMinutes = 10,
          DefaultOrder = AwardConsts.OrderMin,
          VIQOrder = AwardConsts.OrderMin
        }
    );

    // Assert
    result.Success.ShouldBeFalse();
    result.ErrorMessage.ShouldNotBeNullOrWhiteSpace();
    result.Data?.ShouldBeNull();
  }

  [Fact]
  public async Task Should_Not_Create_An_Award_Without_Keyword()
  {
    // Act/Assert
    await Assert.ThrowsAsync<AbpValidationException>(async () =>
    {
      await _awardAppService.CreateAsync(
        new CreateUpdateAwardDto
        {
          Id = 998,
          Keyword = "",
          Abbrev = "TA",
          Title = "Test 2 Award",
          Program = EventProgram.All,
          Type = AwardType.Judged,
          Description = "Test 2 Award description",
          Script = "Test 2 Award script",
          Icon = "Test 2 Award icon url",
          IncludeOnInitialRanking = false,
          IncludeOnFollowUpRanking = false,
          FollowUpInterviewDefaultMinutes = 998,
          DefaultOrder = 999,
          VIQOrder = 999
        }
      );
    });
  }

  [Fact]
  public async Task Should_Not_Create_Award_With_NegativeOrder()
  {
    // This will throw Exception since the CreateUpdateAwardDto requires min & max order values
    // Act/Assert
    await Assert.ThrowsAsync<Volo.Abp.Validation.AbpValidationException>(async () =>
    {
      var result = await _awardAppService.CreateAsync(
        new CreateUpdateAwardDto
        {
          Id = 99,
          Keyword = "TestNegativeOrder",
          Abbrev = "TNO",
          Title = "Test Negative Order",
          Program = EventProgram.All,
          Type = AwardType.Judged,
          Description = "Test Award description",
          Script = "Test Award script",
          Icon = "Test Award icon url",
          IncludeOnInitialRanking = false,
          IncludeOnFollowUpRanking = false,
          FollowUpInterviewDefaultMinutes = 10,
          DefaultOrder = -99,
          VIQOrder = -99
        }
      );
    });
  }

  [Fact]
  public async Task Should_Not_Allow_To_Create_Duplicate_Id()
  {
    // Act
    var result = await _awardAppService.CreateAsync(
        new CreateUpdateAwardDto
        {
          Id = 1,
          Keyword = "Excellence",
          Abbrev = "TA1",
          Title = "Test 1 Award",
          Program = EventProgram.All,
          Type = AwardType.Judged,
          Description = "Test 1 Award description",
          Script = "Test 1 Award script",
          Icon = "Test 1 Award icon url",
          IncludeOnInitialRanking = false,
          IncludeOnFollowUpRanking = false,
          FollowUpInterviewDefaultMinutes = 10,
          DefaultOrder = 1,
          VIQOrder = 1
        }
    );

    // Assert
    result.Success.ShouldBeFalse();
    result.ErrorMessage.ShouldContain("Id");
  }


  [Fact]
  public async Task Should_Not_Allow_To_Create_Duplicate_Keyword()
  {
    // Act
    var result = await _awardAppService.CreateAsync(
          new CreateUpdateAwardDto
          {
            Id = 888,
            Keyword = "Excellence",
            Abbrev = "TA2",
            Title = "Test 2 Award",
            Program = EventProgram.All,
            Type = AwardType.Judged,
            Description = "Test 2 Award description",
            Script = "Test 2 Award script",
            Icon = "Test 2 Award icon url",
            IncludeOnInitialRanking = false,
            IncludeOnFollowUpRanking = false,
            FollowUpInterviewDefaultMinutes = 10,
            DefaultOrder = 88,
            VIQOrder = 88
          }
    );

    // Assert
    result.Success.ShouldBeFalse();
    result.ErrorMessage.ShouldContain("Keyword");
  }

  [Fact]
  public async Task Should_Update_Award()
  {
    // Act 
    var award = await _awardAppService.GetDtoAsync(11); 
    var cuAward = _testAppService.ObjectMapper.Map<AwardDto, CreateUpdateAwardDto>(award);
    cuAward.Keyword = "Update";
    cuAward.Abbrev = "Upd";
    cuAward.Program = EventProgram.V5RC;
    cuAward.Type = AwardType.Performance;
    cuAward.Description = "Desc update";
    cuAward.Script = "Script update";
    cuAward.Icon = "Icon update";
    cuAward.IncludeOnInitialRanking = true;
    cuAward.IncludeOnFollowUpRanking = true;
    cuAward.FollowUpInterviewDefaultMinutes = 25;
    cuAward.DefaultOrder = 20;
    cuAward.VIQOrder = 21;
    var result = await _awardAppService.UpdateAsync(cuAward);
    var newAward = await _awardAppService.GetDtoAsync(11);

    // Assert
    result.Success.ShouldBeTrue();
    newAward.Keyword.ShouldBe("Update");
    newAward.Abbrev.ShouldBe("Upd");
    newAward.Program.ShouldBe(EventProgram.V5RC);
    newAward.Type.ShouldBe(AwardType.Performance);
    newAward.Description.ShouldBe("Desc update");
    newAward.Script.ShouldBe("Script update");
    newAward.Icon.ShouldBe("Icon update");
    newAward.IncludeOnInitialRanking.ShouldBeTrue();
    newAward.IncludeOnFollowUpRanking.ShouldBeTrue();
    newAward.FollowUpInterviewDefaultMinutes.ShouldBe(25);
    newAward.DefaultOrder.ShouldBe(20);
    newAward.VIQOrder.ShouldBe(21);
  }

  [Fact]
  public async Task Should_Delete_Award()
  {
    // Act
    await _awardAppService.DeleteAsync(11).ConfigureAwait(true);
    var result = await _awardAppService.GetDtoAsync(11);

    // Assert
    result.Id.ShouldBe(0);
    // 
  }
}
