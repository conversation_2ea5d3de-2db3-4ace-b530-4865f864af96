﻿using System;
using System.Collections.Generic;
using System.Text;

namespace RoboMentors.Utilities;

public class LoggingService : ILoggingService
{
  private List<string> lines;
  private string startText;
  private DateTime startTime;

  public LoggingService()
  {
    lines = new List<string>();
    startText = string.Empty;
  }

  public List<string> Lines => lines;

  public string Text => ConvertToText();

  private string ConvertToText()
  {
    var sb = new StringBuilder();
    foreach (var line in lines)
    {
      sb.AppendLine(line);
    }
    return sb.ToString();
  }

  public void Reset()
  {
    lines.Clear();
  }

  public void WriteLine(string text = "")
  {
    lines.Add($"{GetTimestamp(DateTime.Now)} UTC {text}");
  }

  private string GetTimestamp(DateTime dateTime)
  {
    return dateTime.ToString("HH:mm:ss.ffffff");
  }

  public void WriteStart(string text)
  {
    startTime = DateTime.Now;
    startText = $"{GetTimestamp(DateTime.Now)} UTC {text}";
  }

  public void WriteEnd(string text)
  {
    var duration = new TimeSpan(DateTime.Now.Ticks - startTime.Ticks);
    lines.Add($"{startText}    {text} ({duration.TotalMilliseconds:N1} mSecs)");
    startTime = DateTime.MinValue;
    startText = string.Empty;
  }
}
