{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "https://localhost:44388/", "sslPort": 44388}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development", "Deployment_Environment": "Local"}}, "RoboMentors.Blazor": {"commandName": "Project", "dotnetRunMessages": "true", "launchBrowser": true, "applicationUrl": "https://localhost:44388/", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}