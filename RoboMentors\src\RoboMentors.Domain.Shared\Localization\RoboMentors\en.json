{
  "Culture": "en",
  "Texts": {
    "AppName": "RoboMentors",

    // General
    "at": "at",
    "Add": "Add",
    "Address": "Address",
    "All": "All",
    "AreYouSure": "Are you sure?",
    "AreYouSureToDelete": "Are you sure you want to delete?  {0}",
    "Award": "Award",
    "Clear": "Clear",
    "Close": "Close",
    "Conflict": "Conflict",
    "Criteria": "Criteria",
    "Details": "Details",
    "Directions": "Directions",
    "Disabled": "Disabled",
    "Division": "Division",
    "Done": "Done",
    "Edit": "Edit",
    "ExpandCollapseGroup": "Expand/Collapse Group",
    "Interview": "Interview",
    "Judges": "Judges",
    "Judging": "Judging",
    "Judging:Remote": "Remote Judging",
    "Minutes": "Minutes",
    "MoveTo": "Move To",
    "New": "New",
    "Next": "Next",
    "NextTeam": "Next Team",
    "NoData": "No data to display",
    "Notes": "Notes",
    "NotesPlaceholder": "Enter your notes here.",
    "Notebook": "notebook",
    "on": "on",
    "PageSize": "Page Size",
    "Pause": "Pause",
    "PleaseEnter": "Please enter",
    "Previous": "Previous",
    "Process": "Process",
    "Ranking": "Ranking",
    "Restart": "Restart",
    "Resume": "Resume",
    "Rubric": "Rubric",
    "Save": "Save",
    "Score": "Score",
    "Scores": "Scores",
    "Search": "Search",
    "Selected": "Selected",
    "Settings": "Settings",
    "SortedBy": "Sorted by",
    "SortedBy:WAvgScore": "Weighted Average Score",
    "SortedBy:%StdDevOfAvg": "% Std Dev of Average",
    "SortedBy:ENRating": "EN Rating",
    "SortedBy:Numerically": "Numerically",
    "SortedBy:Alphanumerically": "Alphanumerically",
    "Start": "Start",
    "Status": "Status",
    "Success": "Success",
    "Team": "Team",
    "Teams": "Teams",
    "Total": "Total",

    // Roles
    "Role:DisabledJudge:Message": "Access to judging has been limited for {0} {1}.  <NAME_EMAIL> if you have any questions or concerns.",
    "Role:Judge": "Judges",
    "Role:Judge:Description": "Judges help decide which teams win awards following a proven evaluation process. It's not hard, is fun and it is surprising what you too can learn from the kids!",
    "Role:JudgeAdvisor": "Judge Advisors",
    "Role:JudgeAdvisor:Description": "Judge Advisors help ensure the judging process goes smoothly. Use the tools provided by this site to help everyone get everything done.",
    "Role:EventPartner": "Event Partner",
    "Role:EventPartner:Description": "Event Partners work together with RECF and key volunteers to make sure events go smoothly.",
    "Role:EventManagement:Events": "Events",
    "Role:Admin": "System Administrator",
    "Role:Admin:Description": "System Administrator manages the system to make sure sytem operates smoothly.",
    "Role:Unknown": "Unknown Role",
    "Role:Unknown:Description": "Your role in the system is unknown at this time. Check out the RECF Volunteer descriptions below and request that role to be added to your profile.",
    "Role:RECLibrary:VolunteersLink": "REC Library Volunteer Descriptions",


    // Drop down Select messages
    "Select:Award": "Select award",
    "Select:AwardType": "Select award type",
    "Select:InterviewType": "Select interview type",
    "Select:JudgeExperience": "Select Judge Experience",
    "Select:Program": "Select program",
    "Select:Region": "Select region",
    "Select:StandardDivisionName": "Select from standard divisions",

    // Menu Items
    "Menu:Home": "Home",
    "Menu:EventManagement": "Event Management",
    "Menu:EventManagement:Events": "Events",
    "Menu:Maintenance": "Maintenance",
    "Menu:Maintenance:EventManagement:Regions": "Regions",
    "Menu:Maintenance:Judging:Awards": "Awards",
    "Menu:Maintenance:Judging:Questions": "Questions",

    // Permissions
    "Permission:EventManagement": "Event Management",
    "Permission:Events": "Event Management",
    "Permission:Events:Create": "Creating new Events",
    "Permission:Events:Edit": "Editing Event Setup",
    "Permission:Events:Users": "Editing Event Users",
    "Permission:Events:Delete": "Deleting Events",
    "Permission:Events:Interview": "Interviews",
    "Permission:Judging": "Judging Management",
    "Permission:Awards": "Awards Management",
    "Permission:Awards:Create": "Creating new Awards",
    "Permission:Awards:Edit": "Editing Awards",
    "Permission:Awards:Delete": "Deleting Awards",
    "Permission:Questions": "Questions Management",
    "Permission:Questions:Create": "Creating new Questions",
    "Permission:Questions:Edit": "Editing Questions",
    "Permission:Questions:Delete": "Deleting Questions",
    "Permission:Regions": "Regions Management",
    "Permission:Regions:Create": "Creating new Regions",
    "Permission:Regions:Edit": "Editing Regions",
    "Permission:Regions:Delete": "Deleting Regions",
    "Permission:Regions:Synchronize": "Synchronizing Regions",

    //*************************************************************************************************************************
    // Home/Index Page
    //*************************************************************************************************************************
    "Welcome": "Welcome to RoboMentors!",
    "Welcome:Auth:Message": "A site dedicated to providng tools for the VEX Robotics Competitions",
    "Welcome:UnAuth:Message": "Dedicated to supporting the mission of the Robotics Education & Competition (REC) Foundation's mission to increase student interest and involvement in science, technology, engineering and mathematics (STEM).",
    "Welcome:Options": "Here are some options to help you get started:",
    "Login": "Login",
    "Login:Tooltip": "Click if you are a returning user",
    "Register": "New to RoboMentors, register here",
    "Register:Tooltip": "Click if you are a new user",
    "VexWorlds:Button": "VEX Robotics World Championship",
    "VexWorlds:Login": "Login to Existing Account",
    "VexWorlds:Login:Tooltip": "Click if you are a returning RoboMentors user",
    "VexWorlds:Register": "New to RoboMentors? Register Here",
    "VexWorlds:Register:Tooltip": "Click if you are a new RoboMentors user",
    "VexWorlds:Volunteer": "Volunteer",
    "VexWorlds:Volunteer:Tooltip": "Click to see your commitments and volunteer to help",
    "VexWorlds:Events": "Go to My Event List",
    "VexWorlds:Events:Tooltip": "Click to go to your list of events you are volunteering at",
    "Home:Question:UnAuth": "How would you like to be involved in a VEX Robotics Competition?",
    "Home:Volunteer": "Volunteer",
    "Home:Volunteer:Title": "How can I help?",
    "Home:Preparations": "Preparations & Help",
    "Home:Preparations:Title": "Are you ready?",
    "Home:Events": "My Events",
    "Home:Events:Title": "Let's get started!",


    //*************************************************************************************************************************
    //* Event Management
    //*************************************************************************************************************************
    // Events
    "Event:Unauthorized": "You are not authorized for this event.",
    "Event:New": "New Event",
    "Event:ShowAll": "Show All",
    "Event:ShowCurrent": "Show Current",
    "Event:Add": "Add",
    "Event:SelectNew": "Select Event(s) to Add",
    "Event:Filter": "Filter by Region and/or Name",
    "Event:Filter:Text": "Name and/or Sku filter",
    "Event:Sku": "Sku",
    "Event:Name": "Event",
    "Event:Dates": "Dates",
    "Event:Start": "From",
    "Event:End": "To",
    "Event:Settings": "Settings",
    "Event:SeasonId": "Season Id",
    "Event:SeasonName": "Season",
    "Event:Level": "Level",
    "Event:Type": "Type",
    "Event:Program": "Program",
    "Event:Address": "Address",
    "Event:Venue": "Venue",
    "Event:Address_1": "Street",
    "Event:City": "City",
    "Event:Region": "Region",
    "Event:Postcode": "Zip",
    "Event:Country": "Country",
    "Event:Latitude": "Lat",
    "Event:Longitude": "Lon",
    "Event:General": "General",
    "Event:Edit:General": "Event General Information",
    "Event:Capacity": "Capacity",
    "Event:PrimaryContact": "Primary Contact",
    "Event:PrimaryName": "Name",
    "Event:PrimaryEmail": "Email",
    "Event:PrimaryPhone": "Phone",
    "Event:Judging": "Judging",
    "Event:Judging:General": "General",
    "Event:Edit:Judging": "Event Judging Information",
    "Event:TargetJudgeCount": "Target # Judges",
    "Event:JudgeInterviewTeamCount": "# Judge Teams",

    "Event:InterviewTimeslotsJson": "Interview Timeslots",
    "Event:InterviewTimeslotsJson:Edit": "Edit Interview Timeslots",
    "Event:InterviewTimeslotsJson:Setup": "Setup Interview Timeslots",
    "Event:InterviewTimeslotsJson:Scheduled": "Scheduled",
    "Event:InterviewTimeslotsJson:NoSchedule": "None",
    "Event:InterviewTimeslotsJson:StartDate": "Start Date",
    "Event:InterviewTimeslotsJson:StartTime": "Start Time",
    "Event:InterviewTimeslotsJson:Minutes": "Minutes",
    "Event:InterviewTimeslotsJson:Slots": "# Slots",
    "Event:InterviewTimeslotsJson:Source": "Source",
    "Event:InterviewTimeslotsJson:Timeslots": "Timeslots",
    "Event:InterviewTimeslotsJson:AddTimeslots": "Add Timeslots",
    "Event:InterviewTimeslotsJson:RemoveAllTimeslots": "Remove All Timeslots",

    "Event:InitialInterviewDefaultMinutes": "Initial Interview Default Minutes",
    "Event:AllowJudging": "Allow Judging",
    "Event:AllowJudgingButton:Disable": "Disable Judging",
    "Event:AllowJudgingButton:Allow": "Allow Judging",
    "Event:Judging:Remote": "Remote",
    "Event:RemoteJudging": "Remote Judging",
    "Event:RemoteJudgingStart": "Remote Judging Start",
    "Event:RemoteJudgingEnd": "Remote Judging End",
    "Event:Divisions": "Divisions",
    "Event:DifferentPlanningDivisions": "Different Planning Divisions",
    "Event:ServerDateTime": "Server",
    "Event:Refreshed": "Event refreshed.",
    "Event:Timezone": "Timezone",
    "Event:StartTimezone": "Start Timezone",
    "Event:EndTimezone": "End Timezone",

    // Event Divisions
    "EventDivision:Edit": "Event Divisions",
    "EventDivision:Setup": "Setup Divisions",
    "EventDivision:EditHelp": "If event has multiple judged divisions not yet setup in RobotEvents, use this to set up planned divisions. Do NOT use for 1 division events.",
    "EventDivision:AddDivision": "Division",
    "EventDivision:AddStandardDivisions": "Standard Divs",
    "EventDivision:PlanningDivisionId": "Planning Id",
    "EventDivision:Name": "Division",
    "EventDivision:Order": "Order",
    "EventDivision:DivisionId": "RE Division",
    "EventDivision:TeamCount": "Planning Id",
    "EventDivision:TeamsToInterview": "Planning Id",
    "EventDivision:JudgeCount": "Planning Id",
    "EventDivision:OKtoDelete": "OK to Delete",
    "EventDivision:StandardNames": "Use a standard division name",

    "EventDivision:Errors:RENotFound": "Not Found",
    "EventDivision:Errors:ThereIsAnAllDivisions:NoAdd": "Division with 'All Divisions', can't add another division.",
    "EventDivision:Errors:ThereIsAnAllDivisions:CantAdd": "Can't add a new division if another division has 'All Divisions'.",
    "EventDivision:Errors:NameMustBeUnique": "Name is required and must be unique. {0} already exists.",
    "EventDivision:Errors:CantDelete": "Judging Division can't be deleted because there is data in and event table for this division.",
    "EventDivision:Errors:REDivisionWillBeUsed": "RobotEvents Divisions will be used as Planning Divisions.",
    "EventDivision:Errors:Order": "Order must be positive.",

    // Event Menu
    "Event:Menu:Main": "Event",
    "Event:Menu:Home": "Me",
    "Event:Menu:Details": "Details",
    "Event:Menu:Awards": "Awards",
    "Event:Menu:Teams": "Teams",
    "Event:Menu:Users": "Volunteers",
    "Event:Menu:Results": "Results",
    "Event:Menu:Judging:Planning": "Planning",
    "Event:Menu:Judging:Planning:InitialInterviews": "Initial Interviews",
    "Event:Menu:Judging:Planning:EngineeringNotebooks": "Noteboooks",
    "Event:Menu:Judging:Planning:AwardInterviews": "Award Interviews",
    "Event:Menu:Judging:Planning:WCAwardRankings": "WC Award Rankings",
    "Event:Menu:Judging:Planning:WCDivisionMaster": "WC Division Master",
    "Event:Menu:Judging:Planning:FollowUpInterviews": "Follow-Up Interviews",
    "Event:Menu:Judging:Interviews": "Interviews",
    "Event:Menu:Judging:Interviews:Initial": "Initial",
    "Event:Menu:Judging:Interviews:Award": "Award",
    "Event:Menu:Judging:Interviews:FollowUp": "Follow-Up",
    "Event:Menu:Judging:Interview": "Interview for {0} - {1}", // TeamNumber - Team Name
    "Event:Menu:Judging:EngineeringNotebooks": "Notebooks",
    "Event:Menu:Judging:EngineeringNotebooks:InitialReviews": "Initial Reviews",
    "Event:Menu:Judging:EngineeringNotebooks:ToScore": "Notebooks to Score",
    "Event:Menu:Judging:EngineeringNotebooks:Rankings": "Rankings",
    "Event:Menu:Judging:Deliberations": "Deliberations",

    // Event Awards
    "EventAward:Edit": "Edit Award Settings",
    "EventAward:Order": "Order",
    "EventAward:Title": "Title",
    "EventAward:Qualifications": "Qualifies for",
    "EventAward:AwardId": "Award",
    "EventAward:Keyword": "Award",
    "EventAward:Type": "Type",
    "EventAward:Grade": "Grade",
    "EventAward:DivisionId": "Division",
    "EventAward:DivisionName": "Division",
    "EventAward:AwardLevel": "Level",
    "EventAward:Confirmed": "Confirmed",
    "EventAward:InitialInterviews": "Initial Interview Settings",
    "EventAward:IncludeOnInitialRanking": "Include on Rankings",
    "EventAward:MinOnInitialRanking": "Minimum Ranked",
    "EventAward:FollowUpInterviews": "Follow-Up Interview Settings",
    "EventAward:IncludeOnFollowUpRanking": "Include on Rankings",
    "EventAward:MinOnFollowUpRanking": "Minimum Ranked",
    "EventAward:FollowUpInterviewDefaultMinutes": "Default Minutes",
    "EventAward:FollowUpInterviewDefaultMinutes:Abbrev": "FU Mins",
    "EventAward:IncludeOnRankings": "Include Rankings",
    "EventAward:IncludeOnInitialRanking:Yes": "Init=Yes",
    "EventAward:IncludeOnInitialRanking:No": "Init=No",
    "EventAward:IncludeOnFollowUpRanking:Yes": "FU=Yes",
    "EventAward:IncludeOnFollowUpRanking:No": "FU=No",
    "EventAward:Winner": "Winner",
    "EventAward:Presenter": "Presenter",
    "EventAward:Refreshed": "Awards refreshed.",
    "EventAward:Updated": "Award updated.",

    // Event Results
    "EventResult:DivisionId": "Division",
    "EventResult:Tab:Skills": "Skills",
    "EventResult:Tab:Matches": "Matches",
    "EventResult:Tab:Rankings": "Rankings",
    "EventResult:Refreshed": "Results refreshed. {0}",
    "EventResult:SwitchDivision": "{0} Division: {1}",

    // Event Matches
    "EventMatch:DivisionId": "Division",
    "EventMatch:Round": "Round",
    "EventMatch:Instance": "Instance",
    "EventMatch:MatchNum": "Match",
    "EventMatch:Scheduled": "Scheduled",
    "EventMatch:Started": "Started",
    "EventMatch:Field": "Field",
    "EventMatch:RedTeam1": "Red 1",
    "EventMatch:RedTeam2": "Red 2",
    "EventMatch:RedScore": "Score",
    "EventMatch:BlueTeam1": "Blue 1",
    "EventMatch:BlueTeam2": "Blue 2",
    "EventMatch:BlueScore": "Score",
    "EventMatch:NoSchedule": "Match schedule not available yet.",
    "EventMatch:ScheduleComplete": "Matches are complete.",
    "EventMatch:FirstUnscored": "First unscored match = {0}.",

    // Event Rankings
    "EventRanking:DivisionId": "Division",
    "EventRanking:Rank": "Rank",
    "EventRanking:TeamNumber": "Team",
    "EventRanking:Wins": "Wins",
    "EventRanking:Losses": "Loss",
    "EventRanking:Ties": "Ties",
    "EventRanking:WP": "WP",
    "EventRanking:AP": "AP",
    "EventRanking:SP": "SP",
    "EventRanking:HighScore": "High",
    "EventRanking:AvgPoints": "Avg",
    "EventRanking:TotalPoints": "Total",

    // Event Skills
    "EventSkill:TeamNumber": "Team",
    "EventSkill:Rank": "Rank",
    "EventSkill:DriverAttempts": "Dvr#",
    "EventSkill:DriverScore": "Dvr High",
    "EventSkill:ProgramAttempts": "Pgm#",
    "EventSkill:ProgramScore": "Pgm High",
    "EventSkill:TotalScore": "Total",

    // Event Teams
    "EventTeam:Number": "Number",
    "EventTeam:Name": "Name",
    "EventTeam:RobotName": "Robot Name",
    "EventTeam:Organization": "Organization",
    "EventTeam:City": "City",
    "EventTeam:Region": "Region",
    "EventTeam:Country": "Country",
    "EventTeam:Grade": "Grade",
    "EventTeam:PlanningDivisionId": "Division",
    "EventTeam:InterviewTeam": "Interview Judge Team",
    "EventTeam:InterviewTeam:Abbrev": "I-Judges",
    "EventTeam:InterviewTime": "Interview Time",
    "EventTeam:InterviewTime:Abbrev": "I-Time",
    "EventTeam:ENRating": "EN Rating",
    "EventTeam:DENLink": "DEN Link",
    "EventTeam:DENLinkUpdatedAt": "DEN Updated",
    "EventTeam:QualifiedForDesignAward": "Design?",
    "EventTeam:QualifiedForExcellenceAward": "Excellence?",
    "EventTeam:FollowUpTeam": "FU-Judges",
    "EventTeam:FollowUpTime": "FU-Time",
    "EventTeam:Refreshed": "Teams refreshed.",
    "EventTeam:InitialInterviewStatus": "Status",

    // Event Users
    "EventUser:New": "+/-",
    "EventUser:Edit": "Edit User",
    "EventUser:Edit:Gen": "General for role as {0}",
    "EventUser:Edit:Jdg": "Judging",
    "EventUser:Current": "Current",
    "EventUser:Available": "Available",
    "EventUser:Header": "  List = Name (Region(s); email)",
    "EventUser:Filter": "Filter by Region, name, and/or email",
    "EventUser:Filter:Text": "Name and/or email filter",
    "EventUser:GeneralInfo": "General Info",
    "EventUser:JudgingInfo": "Judging Info",
    "EventUser:RoleId": "Role",
    "EventUser:Status": "Status",
    "EventUser:Trained": "Trained",
    "EventUser:ResourcesSent": "Resources Sent",
    "EventUser:PlanningDivisionId": "Division",
    "EventUser:InterviewTeam": "Interview",
    "EventUser:AwardTeam": "Award",
    "EventUser:ENTeam": "Notebooks",
    "EventUser:FollowUpTeam": "Follow-Up",
    "EventUser:UserName": "User Name",
    "EventUser:Name": "Name",
    "EventUser:Surname": "Surname",
    "EventUser:Email": "Email",
    "EventUser:PhoneNumber": "Phone",
    "EventUser:Affiliations": "Affiliations",
    "EventUser:JudgeExperience": "Experience",
    "EventUser:RoleName": "Role",

    "EventHome:Profile": "Your Profile",
    "EventHome:Profile:RequestUpdate": "Please verify & edit your profile.",
    "EventHome:Assignments": "Your Event Assignments",
    "EventHome:Role": "Role",
    "EventHome:Status": "Status",
    "EventHome:Division": "Division",
    "EventHome:InterviewTeam": "Interview Judge Team",
    "EventHome:ENJudgeTeam": "Notebook Judge Team",
    "EventHome:AwardTeam": "Award Judge Team",
    "EventHome:FollowUpTeam": "Follow-Up Judge Team",
    "EventHome:TargetStatus:Confirmed": "Verify & edit your profile above. Click here to confirm you will be a {0} at this event.",
    "EventHome:TargetStatus:SignedIn": "Verify & edit your profile above. Click here to sign in as a {0} for this event.",

    "EventUser:RemoveSelf": "If you remove your self as JA, you will lose access to this event.",
    "EventUser:RemoveAlreadyAssigned": "User {0} is already assigned in his/her role for this event. Do you still want to remove this user?",

    // Identity User Profile
    "IdentityUser:Profile:Edit": "Edit Profile",
    "IdentityUser:Profile": "Profile",
    "IdentityUser:Name": "Name",
    "IdentityUser:Surname": "Surname",
    "IdentityUser:Email": "Email",
    "IdentityUser:PhoneNumber": "Phone",

    // Identity User Extensions
    "IdentityUser:Bio": "Bio notes",
    "IdentityUser:JudgeExperience": "Judge Experience",
    "IdentityUser:Affiliations": "Affiliations",
    "IdentityUser:RegionAffiliations": "Region Affiliations * - separate multiple Regions with a comma ",
    "IdentityUser:RegionAffiliations:Placeholder": "Region Affiliations * - separate multiple Regions with a comma ",
    "IdentityUser:RegionAffiliations:Short": "Region(s)",
    "IdentityUser:RegionAffiliations:Edit": "Edit Regions",
    "IdentityUser:TeamAffiliations": "Team Affiliations - separate multiple Teams with a comma ",
    "IdentityUser:TeamAffiliations:Placeholder": "Team Affiliations - separate multiple Teams with a comma ",
    "IdentityUser:TeamAffiliations:Short": "Team(s)",
    "IdentityUser:TeamAffiliations:Edit": "Edit Teams",
    "IdentityUser:AffiliationEdit:Region": "Edit Region Affiliations for",
    "IdentityUser:AffiliationEdit:Team": "Edit Team Affiliations for",

    "IdentityUser:Errors:RegionAffiliations": "Region affiliations is required. Enter regions using the Edit Regions button.",

    // Event Errors
    "Errors:SeeLog": "See Error Log",
    "Errors:Events": "Events Maintenance ERROR",
    "Errors:Events:IdAlreadyExists": "There is already an event with the same id: {0}.  Id must be unique.",
    "Errors:Events:PrimaryName": "Primary name is open",
    "Errors:Events:PrimaryEmail": "Primary email must be valid email address",
    "Errors:Events:PrimaryPhone": "Primary phone must be valid phone number",

    // Regions
    "Region:ClearFilter": "Clear Filter",
    "Region:Filter": "Filter by Text",
    "Region:Filter:Text": "Search",
    "Region:New": "Region",
    "Region:NoRegionsExist": "No regions exist. Click the Sync button to populate the list from Robot Events.",
    "Region:Country": "Country",
    "Region:RegionName": "Region Name",
    "Region:Region": "Region",

    //*************************************************************************************************************************
    //* Event - Security
    //*************************************************************************************************************************
    "Error:Security:NotAuthorized": "You are not authorized for the {0} page.",
    "Error:Security:MustBeEventJA": "You must be an Event Judge Advisor to enter this page. See Event Judge Advisor.",
    "Error:Security:NoDivisionAssignment": "You currently are not assigned to a Judging Division. See Judge Advisor.",
    "Error:Security:Judging:Disabled": "Judging is currently disabled. See Judge Advisor.",
    "Error:Security:Judging:Unassigned": "You currently are not assigned to {0} Judge team. See Judge Advisor.",
    "Error:Security:NoENTeamAssignment": "You currently are not assigned to an Engineering Notebook team. See Judge Advisor.",
    "Error:Security:NoAwardAssignment": "You currently are not assigned to an Award Judge team. See Judge Advisor.",
    "Error:Security:NotDesignOrInnovateAssignment": "You are not assigned to Design or Innovate Award Judge Teams. See Judge Advisor.",
    "Error:Security:DeliberationsNotAuthorized": "You are not authorized for Deliberations. See Judge Advisor",
    "Error:Security:WCAwardRankingsNotAuthorized": "You are not authorized for WC Award Rankings. See Judge Advisor",
    "Error:Security:InvalidTypeParameter": "Invalid Type Parameter submitted. See Judge Advisor.",
    "Error:Security:Judging:NotEventTeam": "Team {0} is not part of this event.",
    "Error:Security:Judging:NoAwards": "Awards not setup to do Rankings. Go to Event/Awards and make sure Awards have proper check box for {0} interviews.",
    "Error:Security:Judging:NoENAwards": "You are not on the Design or Innovate Judge teams or these awards are not setup. See Judge Advisor.",
    "Error:Security:Judging:NoTeams": "Teams not setup. Ask Judge Advisor to go to Event/Teams and make sure Teams have been Refreshed from RobotEvents.",

    //*************************************************************************************************************************
    //* Judging - Awards
    //*************************************************************************************************************************
    "Excellence": "Excellence",
    "Design": "Design",
    "Innovate": "Innovate",
    "Think": "Think",
    "Amaze": "Amaze",
    "Build": "Build",
    "Create": "Create",
    // "Judges": "Judges", - listed above
    "Inspire": "Inspire",
    "Energy": "Energy",
    "Sportsmanship": "Sportsmanship",
    "AllAwards": "All Awards",

    //*************************************************************************************************************************
    //* Judging - Planning
    //*************************************************************************************************************************
    //* Planning - Initial Interviews
    "Judging:Planning:InitialInterview:RescheduleAllTeams": "Reschedule All",
    "Judging:Planning:InitialInterview:ScheduleUnassignedTeams": "Schedule Unassigned",
    "Judging:Planning:InitialInterview:AdjustJudgeTeams": "Judge Teams",
    "Judging:Planning:InitialInterview:JudgeTeams": "Judge Teams",
    "Judging:Planning:InitialInterview:InterviewTeam": "Interview Team",
    "Judging:Planning:InitialInterview:Time": "Time",
    "Judging:Planning:InitialInterview:RestrictGradeTo": "Restrict Judge Team to this Grade for Schedule buttons",
    "Judging:Planning:InitialInterview:RescheduleAfterStartedTitle": "Team {0}'s interview has already started.",
    "Judging:Planning:InitialInterview:RescheduleAfterStarted": "Current status is {0}.  Do you still want to move this team?",
    "Judging:Planning:InitialInterview:RescheduleAllAfterStartedTitle": "Interviews have already started!",
    "Judging:Planning:InitialInterview:RescheduleAllAfterStarted": "{0} interviews have already been started.  Do you really want to reschedule all teams?",
    "Judging:Planning:InitialInterview:RescheduleAllTitle": "Reschedule All Teams",
    "Judging:Planning:InitialInterview:RescheduleAllConfirmation": "Current schedule will be lost. Are you sure you want to continue?",
    "Judging:Planning:InitialInterview:ByAwardCount": "How do you want to distribute teams?  Standard or Based on current season award counts?  'By Award Count' will take a few minutes to complete.",
    "Judging:Planning:InitialInterview:Confirm:Standard": "Standard",
    "Judging:Planning:InitialInterview:Confirm:ByAwardCount": "By Award Count",
    "Judging:Planning:InitialInterview:Schedule_Unassigned_Done": "Schedule Unassigned Done",
    "Judging:Planning:InitialInterview:Reschedule_All_Done": "Reschedule All Done",

    "Error:Planning:InitialInterview:InterviewsAlreadyStarted": "Interviews have already started. Can't remove judge team.",

    //* Planning - Engineering Notebooks
    "Judging:Planning:EngineeringNotebook:Title": "Planning Engineering Notebooks",
    "Judging:Planning:EngineeringNotebook:CopyInitialInterviewTeams": "Copy Initial Interview Judge Teams",
    "Judging:Planning:EngineeringNotebook:CopyInitialTeamsAfterStart": "There have already been {0} ENR scored.  Do you still want to copy Judge teams from Initial Interviews?",
    "Judging:Planning:EngineeringNotebook:CopyInitialTeamsAfterStart:Title": "EN Reviews have already been started!",
    "Judging:Planning:EngineeringNotebook:ChangeSort": "Change Sort",
    "Judging:Planning:EngineeringNotebook:WAvgScore": "WAvg Score",
    "Judging:Planning:EngineeringNotebook:%StdDevOfAvg": "%SD of Avg",
    "Judging:Planning:EngineeringNotebook:FullyDeveloped": "Fully Developed Notebooks",
    "Judging:Planning:EngineeringNotebook:ENStatus:C": "Completed",
    "Judging:Planning:EngineeringNotebook:ENStatus:ErrFD": "FD Error",
    "Judging:Planning:EngineeringNotebook:ENStatus:ErrFD:Tip": "FD criteria not met. Zero in Identify, Brainstorm, Select and/or Build/Program.",
    "Judging:Planning:EngineeringNotebook:ENStatus:D": "Deleted",
    "Judging:Planning:EngineeringNotebook:ENStatus:S": "Started",
    "Judging:Planning:EngineeringNotebook:ENStatus:IP": "In Process",
    "Judging:Planning:EngineeringNotebook:ENStatus:AC": "Almost Complete",
    "Judging:Planning:EngineeringNotebook:NotebooksScored": "Notebooks Scored",
    "Judging:Planning:EngineeringNotebook:JudgeTeamAvgScores": "Judge Team Avg Scores",
    "Judging:Planning:EngineeringNotebook:RubricReview:Title": "Engineering Notebook Rubric JA Review",
    "Judging:Planning:EngineeringNotebook:RubricReview:NotSaved": "Review ONLY, NOT SAVED",

    "Error:Planning:EngineeringNotebook:ReviewsAlreadyStarted": "Notebook reviews have already started. Can't remove judge team.",

    //* Planning - General
    "Judging:Planning:JudgeTeams:Adjust": "Judge Teams",
    "Judging:Planning:JudgeTeams:RemoveJudgeTeamWithAssignedTeamsTitle": "Remove Judge Team?",
    "Judging:Planning:JudgeTeams:RemoveJudgeTeamWithAssignedTeams": "{0} teams have already been assigned.  Do you really want to remove Judge Team {1}?",

    "Error:Planning:JudgeTeams:Increment": "Can't add/remove Judge teams by {0} as you have reached the max of 0-52.",


    //*************************************************************************************************************************
    //* Judging - Interviews
    //*************************************************************************************************************************
    "Judging:Interviews:Title": "Interview for {0} - {1}", // TeamNumber - Team Name
    "Judging:Interviews:PreviewProcessQuestionsObservations": "Preview Process/Questions/Observations",
    "Judging:Interviews:AwardRanking": "Review Award Rankings",
    "Judging:Interviews:Pictures": "Pictures",
    "Judging:Interviews:Process:Show": "Show Process",
    "Judging:Interviews:Process:Hide": "Hide Process",
    "Judging:Interviews:Observations:Show": "Show Observations",
    "Judging:Interviews:Observations:Hide": "Hide Observations",
    "Judging:Interviews:SeeNotesAtBottomOfPage": "See NOTES at bottom of page",

    // * InterviewHelp Modal
    "Judging:Interviews:Help:Title:General": "Initial Interviews",
    "Judging:Interviews:Help:Title:FollowUp": "Follow-Up Interviews",
    "Judging:Interviews:Process:SubTitle:FollowUp": "Follow-Up Interview",
    "Judging:Interviews:Process:Recommended": "Recommended Process",
    "Judging:Interviews:Questions:Title": "Questions",
    "Judging:Interviews:Observations:Title": "Observations",

    "Error:Interviews:TIRNotScored": "Complete the Team Interview Rubric to continue to Rankings.",

    // * Interview Timer
    "Judging:Interviews:Confirmation:Header": "Are the following true:",
    "Judging:Interviews:Confirmation:Team": "Team is {0} - {1} ({2})?",
    "Judging:Interviews:Confirmation:AllPresent": "Appropriate team members present?",
    "Judging:Interviews:Confirmation:Time": "Enough time for {0} minute interview before next match?",
    "Judging:Interviews:Confirmation:Cancel": "Cancel Interview",
    "Judging:Interviews:Timer:Start": "Start Interview for {0}",
    "Judging:Interviews:Timer:TimeLeft": "Time Left",
    "Judging:Interviews:Timer:InterviewTimer": "Interview Timer",
    "Judging:Interviews:Timer:TimeExpired": "Time Expired",

    // * Rubrics
    "Judging:Rubric:Criteria": "Criteria",
    "Judging:Rubric:EDP": "EDP = Engineering Design Process",
    "Judging:Rubric:ProficiencyLevel": "Proficiency Level",
    "Judging:Rubric:Expert": "Expert",
    "Judging:Rubric:Proficient": "Proficient",
    "Judging:Rubric:Emerging": "Emerging",
    "Judging:Rubric:Points": "Points",
    "Judging:Rubric:Expert:Points": "(4-5 points)",
    "Judging:Rubric:Proficient:Points": "(2-3 points)",
    "Judging:Rubric:Emerging:Points": "(0-1 points)",
    "Judging:Rubric:TotalPoints": "Total Points",
    "Judging:Rubric:Confidential": "All Judging materials are strictly confidential. They are not shared beyond the Judges/Judge Advisor and shall be destroyed at the end of the event.",
    //* Team Interview Rubrics - TIR
    "Judging:TIR:Title": "Team Interview Rubric",
    "Judging:TIR:EDP": "Engineering Design Process",
    "Judging:TIR:Strategy": "Game Strategies",
    "Judging:TIR:RobotDesign": "Robot Design",
    "Judging:TIR:RobotBuild": "Robot Build",
    "Judging:TIR:Programming": "Robot Programming",
    "Judging:TIR:Project": "Team and Project Management",
    "Judging:TIR:Teamwork": "Teamwork, Communication, Professionalism",
    "Judging:TIR:Respect": "Respect, Courtesy, Positivity",
    "Judging:TIR:SpecialNotes": "Special Attributes and Overall Impressions",
    "Judging:TIR:SpecialNotesPlaceholder": "Enter special attributes and overall impressions here.",

    // * Award Rankings
    "Judging:AwardRankings:Title": "Award Rankings",
    "Judging:AwardRankings:Preliminary": "Preliminary Ranking",
    "Judging:AwardRankings:Final": "Final Rankings",
    "Judging:AwardRankings:NoAwardsToRank:Title": "No Awards to Rank",
    "Judging:AwardRankings:NoAwardsToRank:Directions": "No awards to rank for team {0}. Click 'Next Team' to continue.",
    "Judging:AwardRankings:LastTeamInterviewed": "You just finished your last interview.",
    "Judging:AwardRankings:RankTeams": "Make sure teams are in the correct RANK order.",
    "Judging:AwardRankings:CheckNominations": "Check the box to NOMINATE teams for this award. You must check a minimum of {0} team(s).",
    "Judging:AwardRankings:AskJAForNumberOfTeams": "Ask Judge Advisor for number of teams needed.",
    "Judging:AwardRankings:CheckAwardFinalists": "Rank teams in final order for award consideration.",
    "Judging:AwardRankings:LastTeamThatWasInterviewed": "*Last team that was interviewed",
    "Judging:AwardRankings:LastTeamMoved": "**Last team moved",
    "Judging:AwardRankings:NotesPictures": "Notes and/or Pictures",
    "Judging:AwardRankings:NominationsSubmitted": "You have successfully submitted your award rankings. Please see the Judge Advisor for the next steps.",
    "Judging:AwardRankings:LastENTeam": "Check Interview box for the top 5 or 20% of teams with Outstanding Notebooks. Click Done to go on to the EN Interview page.",
    "Judging:AwardRankings:WaitForAllFDScores": "Waiting for all Fully Developed Notebooks to be scored. Please score remaining notebooks and come back to rankings.",
    "Judging:AwardRankings:NotScoredYet": "Not Scored Yet",
    "Judging:AwardRankings:NotScored": "Not Scored",


    "Error:AwardRankings:MissingNominations": "ERROR - Nomination requirement not met",
    "Error:AwardRankings:MissingNominations:Details": "You must nominate a minimum of {0} teams. Please check the box next to the top teams that should be considered for this award.",
    "Error:AwardRankings:NotAllFDENScored": "ERROR - Score ALL Fully Developed Notebooks",
    "Error:AwardRankings:NotAllFDENScored:Detail": "You must score all Fully Developed Notebooks before you can proceed with selecting teams for Follow-Up Interviews.",

    //* Engineering Notebooks
    //* EN Initial Reviews
    "Judging:ENInitialReviews:Title": "Engineering Notebook Initial Reviews",
    "Judging:ENInitialReviews:Qualified": "Teams Qualified for DEN Reviews",
    "Judging:ENInitialReviews:NotQualified": "Teams NOT Qualified for DEN Reviews",
    "Judging:ENInitialReviews:RefreshQualified": "Refresh Qualified",
    "Judging:ENInitialReviews:SortTeamNumerically": "Sort Numerically",
    "Judging:ENInitialReviews:SortTeamAlphaNumerically": "Sort Alphanumerically",
    "Judging:ENInitialReviews:QualifiedTeamsUpdated": "{0} teams were updated.",
    "Judging:ENInitialReviews:TargetNumbers": "Target Fully Developed to be 20-30% or {0} - {1} notebooks.  Current = {2}",
    "Judging:ENInitialReviews:DENLink": "DEN Link",
    "Judging:ENInitialReviews:DENLinkNotAvailable": "DEN - No Link",
    "Judging:ENInitialReviews:LastTeamMoved": "**Last Team Moved",

    //* EN List to Score
    "Judging:ENListToScore:Title": "Engineering Notebooks to Score",
    "Judging:ENListToScore:ENTeam": "EN Judge Team",
    "Judging:ENListToScore:PreviewRubric": "Preview Engineering Notebook Rubric",
    "Judging:ENListToScore:PreviewRubric:Title": "Engineering Notebook Rubric Preview",
    "Judging:ENListToScore:PreviewRubric:NotSaved": "Preview ONLY, NOT SAVED",
    "Judging:ENListToScore:RubricScores": "Rubric Scores",
    "Judging:ENListToScore:OurStatus": "Our Status",
    "Judging:ENListToScore:OurScore": "Our Score",
    "Judging:ENListToScore:NotCompletedByUs": "Not Completed by Us",
    "Judging:ENListToScore:AvoidConflicts": "Our Conflicts to Avoid",
    "Judging:ENListToScore:CompletedByUs": "Completed by Us",
    "Judging:ENListToScore:StarTip:Conflict": "You have a conflict. You should not score.",
    "Judging:ENListToScore:StarTip:MyInProgress": "You started. Please finish scoring and Mark Complete.",
    "Judging:ENListToScore:StarTip:InProgress": "Scoring is in progress by another judge team.",
    "Judging:ENListToScore:StarTip:Deleted": "You marked as deleted.",
    "Judging:ENListToScore:StarTip:NotScored": "You haven't scored yet.",
    "Judging:ENListToScore:StarTip:LowScore": "You recorded a low score.",
    "Judging:ENListToScore:StarTip:Success": "You scored this notebook.",

    //* ENRubric
    "Judging:ENRubric:Title": "Engineering Notebook Rubric for {0} - {1} ({2})", // TeamNumber - TeamName (Grade)
    "Judging:ENRubric:NotFDTeam": "Team {0} - {1} was not rated as a Fully Developed notebook. Scores will not be included in other pages.",
    "Judging:ENRubric:MustMarkComplete": "Please 'Mark Complete' to go to next Notebook.",
    "Judging:ENRubric:DENLink": "DEN Link",
    "Judging:ENRubric:DENLinkNotAvailable": "DEN - No Link",
    "Judging:ENRubric:SeeNotesAtBottomOfPage": "See NOTES at bottom of page",
    "Judging:ENRubric:ReviewOnly": "For your review only, scoring is disabled. Have original judge team modify score if needed.",
    "Judging:ENRubric:RubricMarkedAsDeleted": "This rubric is marked as DELETED. Scoring has been zeroed & disabled. Undelete at bottom of page to score.",
    "Judging:ENRubric:ConfirmJudgeCount": "Your Judge Team includes {0}.  Please confirm your Judge Team count",
    "Judging:ENRubric:ConfirmJudgeCount:Text": "If this number is different than expected, see Judge Advisor to make sure your judges are assigned to the correct judge team.",
    "Judging:ENRubric:MarkComplete": "Mark Complete",
    "Judging:ENRubric:ZeroValuesFound": "A score of zero (0) was found in: ",
    "Judging:ENRubric:ZeroValuesNoNotes": "If you would like to continue, please enter comments in NOTES field as to why 0 value was scored.",
    "Judging:ENRubric:ZeroValues": "EN Rubric Zero Values",
    "Judging:ENRubric:ZeroValuesConfirmation": "Did you record proper notes regarding these zero values in the NOTES field?",
    "Judging:ENRubric:LowTotalScore": "Engineering Notebook score was low. Are you sure you want to continue with this score?",
    "Judging:ENRubric:Scores": "EN Rubric Scores",
    "Judging:ENRubric:Delete": "Delete?",
    "Judging:ENRubric:MarkAsDeleted": "Delete this rubric? Click checkbox to mark this rubric as deleted. Scores will be zeroed & not included. Current rubric status is {0}.",
    "Judging:ENRubric:ConfirmDelete": "All values will be zeroed and the scores will not be included in the weighted averages. Do you want to continue?",

    "Error:ENRubric:NotFDTeam": "Team {0} - {1} was not rated as a Fully Developed notebook. Returning to event home page.",

    //* Engineering Notebook Rubrics - ENR
    "Judging:ENR:Title": "Engineering Notebook Rubric",
    "Judging:ENR:EDP:IdentifyTheProblem": "EDP: Identify the Problem",
    "Judging:ENR:EDP:BrainstormDiagramOrPrototypeSolutions": "EDP: Brainstorm, Diagram, or Prototype Solutions",
    "Judging:ENR:EDP:SelectBestSolutionAndPlan": "EDP: Select Best Solution and Plan",
    "Judging:ENR:EDP:BuildAndProgramTheSolution": "EDP: Build and Program the Solution",
    "Judging:ENR:EDP:TestSolution": "EDP: Test Solution",
    "Judging:ENR:EDP:RepeatDesignProcess": "EDP: Repeat Design Process",
    "Judging:ENR:InnovationOriginality": "Innovation/Originality",
    "Judging:ENR:UseabilityAndCompleteness": "Useability and Completeness",
    "Judging:ENR:RecordOfTeamAndProjectManagement": "Record of Team and Project Management",
    "Judging:ENR:NotebookFormat": "Notebook Format",

    //*************************************************************************************************************************
    //* Judging - Maintenance
    //*************************************************************************************************************************
    // Awards
    "Award:New": "Award",
    "Award:View": "View Award",
    "Award:Edit": "Edit Award",
    "Award:Id": "Id",
    "Award:Keyword": "Keyword",
    "Award:Abbrev": "Abbrev",
    "Award:Title": "Title",
    "Award:Program": "Program",
    "Award:Type": "Type",
    "Award:Description": "Description",
    "Award:Script": "Script",
    "Award:Icon": "Icon",
    "Award:IconUrl": "Icon URL",
    "Award:IncludeOnInitialRanking": "Include on Initial Ranking",
    "Award:IncludeOnFollowUpRanking": "Include on Follow-Up Ranking",
    "Award:FollowUpInterviewDefaultMinutes": "FU Default Mins",
    "Award:DefaultOrder": "Order",
    "Award:VIQOrder": "IQ Order",
    "Award:IncludeDefaults": "Include Defaults",
    "Award:IncludeOnInitialRanking:Yes": "Init=Yes",
    "Award:IncludeOnInitialRanking:No": "Init=No",
    "Award:IncludeOnFollowUpRanking:Yes": "FU=Yes",
    "Award:IncludeOnFollowUpRanking:No": "FU=No",

    "Errors:Awards": "Awards Maintenance ERROR",
    "Errors:Awards:IdAlreadyExists": "There is already an award with the same id: {0}.  Id must be unique.",
    "Errors:Awards:Required": "Keyword is required.",
    "Errors:Awards:KeywordAlreadyExists": "There is already an award with the same keyword: {0}.  Keyword must be unique.",
    "Errors:Awards:Order": "Order must be between {0} and {1}.",
    "Errors:Awards:Description": "Description is required.",
    "Errors:Awards:Script": "Script is required.",
    "Errors:Awards:IconUrl": "Icon's url is required.",
    "Errors:Awards:FollowUpInterviewDefaultMinutes": "FU Interview default minutes must be between {0} and {1}.",

    // Questions
    "Question:New": "Question",
    "Question:View": "View Question",
    "Question:Edit": "Edit Question",
    "Question:Filter": "Filter by Interivew, Award, and/or Text",
    "Question:Filter:Text": "Text filter",
    "Question:Id": "Id",
    "Question:InterviewType": "Interview Type",
    "Question:InterviewType:Abbrev": "Interview",
    "Question:Award": "Award",
    "Question:Order": "Order",
    "Question:Text": "Text",
    "Question:Text:Placeholder": "Enter question text here.",
    "Question:NoteKeyword": "Note Keyword(s)",
    "Question:NoteKeyword:Placeholder": "Enter keyword(s) for notes here.",

    "Errors:Questions": "Questions Maintenance ERROR",
    "Errors:Questions:Order": "Order must be positive.",
    "Errors:Questions:Text": "Question text is required.",

    //*************************************************************************************************************************
    // Enums - Start
    //*************************************************************************************************************************
    // Enums - AwardLevel
    "Enum:AwardLevel:1": "Event",
    "Enum:AwardLevel:2": "Division",

    // Enums - AwardType
    "Enum:AwardType:0": "Judged",
    "Enum:AwardType:1": "Performance",

    // Enums - ENRating
    "Enum:ENRating:0": "Not Submitted",
    "Enum:ENRating:1": "Needs Review",
    "Enum:ENRating:2": "Not Minimum",
    "Enum:ENRating:3": "Developing",
    "Enum:ENRating:4": "Borderline",
    "Enum:ENRating:5": "Fully Developed",
    "Enum:ENRating:Tooltip:0": "Notebooks have not been recorded as submitted.",
    "Enum:ENRating:Tooltip:1": "Notebooks have been submitted, but still needs to be reviewed. This column should be empty when reviews are complete.",
    "Enum:ENRating:Tooltip:2": "Notebooks do not meet the minimum requirements of a Fully Developed notebook. Must have the first four criteria of the rubric outlining the initial design process of a single iteration.",
    "Enum:ENRating:Tooltip:3": "Notebooks contain little detail, will have few drawings, and will not be a complete record of the design process.",
    "Enum:ENRating:Tooltip:4": "Notebooks are borderline between Developing and Fully Developed. These won't be scored, but are first choice for Fully Developed if more are needed to meet the 20-30% target.",
    "Enum:ENRating:Tooltip:5": "Notebooks contain great detail, and will include detailed drawings, tests and test results, solutions to problems the team encountered, and will be a complete record of the design process.",

    // Enums - EventJudgingStatus
    "Enum:EventJudgingStatus:0": "Unknown",
    "Enum:EventJudgingStatus:1": "Planning",
    "Enum:EventJudgingStatus:2": "Active",
    "Enum:EventJudgingStatus:3": "Disabled",
    "Enum:EventJudgingStatus:4": "Closed",

    // Enums - EventLevel
    "Enum:EventLevel:0": "World",
    "Enum:EventLevel:1": "National",
    "Enum:EventLevel:2": "Regional",
    "Enum:EventLevel:3": "State",
    "Enum:EventLevel:4": "Signature",
    "Enum:EventLevel:5": "Other",

    // Enums - EventProgram
    "Enum:EventProgram:0": "Program Error",
    "Enum:EventProgram:1": "VRC",
    "Enum:EventProgram:4": "VEXU",
    "Enum:EventProgram:37": "Workshop",
    "Enum:EventProgram:41": "VIQC",
    "Enum:EventProgram:42": "DIS",
    "Enum:EventProgram:43": "NRL",
    "Enum:EventProgram:44": "ADC",
    "Enum:EventProgram:46": "TVRC",
    "Enum:EventProgram:47": "TIQC",
    "Enum:EventProgram:48": "VAIC-HS",
    "Enum:EventProgram:49": "VAIC-U",
    "Enum:EventProgram:51": "VRAD",
    "Enum:EventProgram:52": "VGOC",
    "Enum:EventProgram:53": "V123C",
    "Enum:EventProgram:55": "Bell AVR",
    "Enum:EventProgram:56": "FAC",
    "Enum:EventProgram:57": "VAIC",
    "Enum:EventProgram:998": "New Program Encountered",
    "Enum:EventProgram:999": "All",

    // Enums - EventType
    "Enum:EventType:0": "Tournament",
    "Enum:EventType:1": "League",
    "Enum:EventType:2": "Workshop",
    "Enum:EventType:3": "Virtual",

    // Enums - FileType
    "Enum:FileType:1": "Google Sheets",
    "Enum:FileType:2": "RobotEvents API",

    // Enums - InterviewType
    "Enum:InterviewType:1": "Initial",
    "Enum:InterviewType:2": "Award",
    "Enum:InterviewType:3": "FollowUp",
    "Enum:InterviewType:10": "Other",
    "Enum:InterviewType:20": "Remote Initial",

    // Enums - Grade
    "Enum:Grade:0": "College",
    "Enum:Grade:Abbrev:0": "U",
    "Enum:Grade:1": "High School",
    "Enum:Grade:Abbrev:1": "HS",
    "Enum:Grade:2": "Middle School",
    "Enum:Grade:Abbrev:2": "MS",
    "Enum:Grade:3": "Elementary School",
    "Enum:Grade:Abbrev:3": "ES",
    "Enum:Grade:99": "All",
    "Enum:Grade:Abbrev:99": "All",
    "Enum:Grade:-1": "ERROR",
    "Enum:Grade:Abbrev:-1": "ERR",

    // Enums - ImportType
    "Enum:ImportType:1": "DEN Links",
    "Enum:ImportType:2": "Judge List",
    "Enum:ImportType:3": "Remote Interviews",
    "Enum:ImportType:4": "Team Divisions",
    "Enum:ImportType:5": "World Awards",
    "Enum:ImportType:6": "World Skills",
    "Enum:ImportType:21": "Sync RobotEvents Performance",
    "Enum:ImportType:99": "All",

    // Enums - JudgeExperience
    "Enum:JudgeExperience:0": "New (Never Judged)",
    "Enum:JudgeExperience:1": "FIRST or Other Student Competitions",
    "Enum:JudgeExperience:2": "Local RECF Events",
    "Enum:JudgeExperience:3": "Signature/Championship RECF Events",
    "Enum:JudgeExperience:4": "VEX Worlds",

    // Enums - JudgeExperience - for ABP screens
    "JudgeExperience.New": "New (Never Judged)",
    "JudgeExperience.OtherCompetitions": "FIRST or Other Student Competitions",
    "JudgeExperience.LocalRECF": "Local RECF Events",
    "JudgeExperience.SignatureOrChampionshipEdit": "Signature/Championship RECF Events",
    "JudgeExperience.VEXWorlds": "VEX Worlds",

    // Enums - JudgeTeam
    "Enum:JudgeTeam:-1": "Unavailable",
    "Enum:JudgeTeam:0": "Unassigned",
    "Enum:JudgeTeam:1": "A",
    "Enum:JudgeTeam:2": "B",
    "Enum:JudgeTeam:3": "C",
    "Enum:JudgeTeam:4": "D",
    "Enum:JudgeTeam:5": "E",
    "Enum:JudgeTeam:6": "F",
    "Enum:JudgeTeam:7": "G",
    "Enum:JudgeTeam:8": "H",
    "Enum:JudgeTeam:9": "I",
    "Enum:JudgeTeam:10": "J",
    "Enum:JudgeTeam:11": "K",
    "Enum:JudgeTeam:12": "L",
    "Enum:JudgeTeam:13": "M",
    "Enum:JudgeTeam:14": "N",
    "Enum:JudgeTeam:15": "O",
    "Enum:JudgeTeam:16": "P",
    "Enum:JudgeTeam:17": "Q",
    "Enum:JudgeTeam:18": "R",
    "Enum:JudgeTeam:19": "S",
    "Enum:JudgeTeam:20": "T",
    "Enum:JudgeTeam:21": "U",
    "Enum:JudgeTeam:22": "V",
    "Enum:JudgeTeam:23": "W",
    "Enum:JudgeTeam:24": "X",
    "Enum:JudgeTeam:25": "Y",
    "Enum:JudgeTeam:26": "Z",
    "Enum:JudgeTeam:27": "AA",
    "Enum:JudgeTeam:28": "BB",
    "Enum:JudgeTeam:29": "CC",
    "Enum:JudgeTeam:30": "DD",
    "Enum:JudgeTeam:31": "EE",
    "Enum:JudgeTeam:32": "FF",
    "Enum:JudgeTeam:33": "GG",
    "Enum:JudgeTeam:34": "HH",
    "Enum:JudgeTeam:35": "II",
    "Enum:JudgeTeam:36": "JJ",
    "Enum:JudgeTeam:37": "KK",
    "Enum:JudgeTeam:38": "LL",
    "Enum:JudgeTeam:39": "MM",
    "Enum:JudgeTeam:40": "NN",
    "Enum:JudgeTeam:41": "OO",
    "Enum:JudgeTeam:42": "PP",
    "Enum:JudgeTeam:43": "QQ",
    "Enum:JudgeTeam:44": "RR",
    "Enum:JudgeTeam:45": "SS",
    "Enum:JudgeTeam:46": "TT",
    "Enum:JudgeTeam:47": "UU",
    "Enum:JudgeTeam:48": "VV",
    "Enum:JudgeTeam:49": "WW",
    "Enum:JudgeTeam:50": "XX",
    "Enum:JudgeTeam:51": "YY",
    "Enum:JudgeTeam:52": "ZZ",
    "Enum:JudgeTeam:97": "Remote Judges",
    "Enum:JudgeTeam:98": "Notebooks",
    "Enum:JudgeTeam:99": "Design",
    "Enum:JudgeTeam:999": "All",

    // Enums - JudgingStatus
    "Enum:JudgingStatus:-2": "Deleted",
    "Enum:JudgingStatus:-1": "Cancelled",
    "Enum:JudgingStatus:0": "Not Started",
    "Enum:JudgingStatus:1": "Started",
    "Enum:JudgingStatus:2": "Paused",
    "Enum:JudgingStatus:3": "Resumed",
    "Enum:JudgingStatus:4": "Times Up",
    "Enum:JudgingStatus:5": "Ended",
    "Enum:JudgingStatus:6": "Rubric",
    "Enum:JudgingStatus:7": "Scored",
    "Enum:JudgingStatus:8": "Ranked",

    // Enums - JudgingStatus-ENRubric
    "Enum:JudgingStatus:ENRubric:-2": "Deleted",
    "Enum:JudgingStatus:ENRubric:-1": "Cancelled",
    "Enum:JudgingStatus:ENRubric:0": "Not Started",
    "Enum:JudgingStatus:ENRubric:1": "Started",
    "Enum:JudgingStatus:ENRubric:2": "Paused",
    "Enum:JudgingStatus:ENRubric:3": "Resumed",
    "Enum:JudgingStatus:ENRubric:4": "Times Up",
    "Enum:JudgingStatus:ENRubric:5": "Ended",
    "Enum:JudgingStatus:ENRubric:6": "Completed",
    "Enum:JudgingStatus:ENRubric:7": "Scored",
    "Enum:JudgingStatus:ENRubric:8": "Ranked",

    // Enums - StandardPlanningDivisions
    "Enum:StandardPlanningDivision:-1": "Unavailable",
    "Enum:StandardPlanningDivision:0": "Unassigned",
    "Enum:StandardPlanningDivision:1": "Default",
    "Enum:StandardPlanningDivision:2": "Science",
    "Enum:StandardPlanningDivision:3": "Technology",
    "Enum:StandardPlanningDivision:4": "Research",
    "Enum:StandardPlanningDivision:5": "Engineering",
    "Enum:StandardPlanningDivision:6": "Arts",
    "Enum:StandardPlanningDivision:7": "Math",
    "Enum:StandardPlanningDivision:8": "Design",
    "Enum:StandardPlanningDivision:9": "Innovate",
    "Enum:StandardPlanningDivision:10": "Opportunity",
    "Enum:StandardPlanningDivision:11": "Spirit",
    "Enum:StandardPlanningDivision:97": "Remote",
    "Enum:StandardPlanningDivision:98": "Notebooks",
    "Enum:StandardPlanningDivision:99": "All Divisions",

    // Enums - Status
    "Enum:Status:0": "None",
    "Enum:Status:1": "Invited",
    "Enum:Status:2": "Requesting",
    "Enum:Status:3": "Commited",
    "Enum:Status:4": "Confirmed",
    "Enum:Status:5": "Signed In",
    "Enum:Status:6": "No Show",

    // Enums - TimeslotSource
    "Enum:TimeslotSource:0": "Judge Advisor",
    "Enum:TimeslotSource:1": "Judge Team",
    "Enum:TimeslotSourceAbbr:0": "JA",
    "Enum:TimeslotSourceAbbr:1": "JT",

    //*************************************************************************************************************************
    // Enums - End
    //*************************************************************************************************************************
    "End": "End"

  }
}
