using RoboMentors.EventManagement.RobotEvents;
using Shouldly;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Modularity;
using Xunit;

namespace RoboMentors.EventManagement.Teams;

public abstract class TeamAppService_Tests<TStartupModule> : RoboMentorsApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
{
  private readonly ITeamAppService _teamAppService;

  protected TeamAppService_Tests()
  {
    _teamAppService = GetRequiredService<ITeamAppService>();
  }

  [Fact]
  public async Task Should_Sync_And_Get_Teams()
  {
    // Act
    var programs = new List<EventProgram>() { EventProgram.VAIRC };
    var results1 = await _teamAppService.RequestSyncTeamsAsync(programs);
    var results2 = await _teamAppService.GetProgramTeamNumbersAsync();

    //Assert
    results1.Success.ShouldBeTrue();
    results2.Count.ShouldBeGreaterThan(0);
  }

}
