﻿using Newtonsoft.Json;
using RoboMentors.EventManagement.RobotEvents;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;

namespace RoboMentors.Managers.Apis.RobotEvents;

public partial class RobotEventsEventManager : RobotEventsBaseManager, IRobotEventsEventManager
{
  private readonly string _baseUrl;

  public RobotEventsEventManager() : base()
  {
    _baseUrl = RobotEventConsts.REBaseUrl;
  }

  protected JsonSerializerSettings JsonSerializerSettings { get { return _settings.Value; } }

  public static RobotEventsEventManager Create()
  {
    return new RobotEventsEventManager();
  }

  /// <param name="id">Filter by Event ID</param>
  /// <returns>List of Events</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<REEvent>> GetEventsAsync(IEnumerable<int> id)
  {
    return await GetEventsAsync(id, null, null, null, null, null, null, null, null, CancellationToken.None).ConfigureAwait(false);
  }
  /// <param name="start">Filter by Start Date of the Event</param>
  /// <returns>List of Events</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<REEvent>> GetEventsAsync(DateTime start)
  {
    List<EventType> eventTypes = new List<EventType> { EventType.Tournament, EventType.League, EventType.Virtual };
    return await GetEventsAsync(null, null, null, null, start, null, null, null, eventTypes, CancellationToken.None).ConfigureAwait(false);
  }
  /// <param name="id">Filter by Event ID</param>
  /// <param name="sku">Filter by Event SKU</param>
  /// <param name="team">Filter by Teams that participated in the Event</param>
  /// <param name="season">Filter by the Season that the Event belonged to</param>
  /// <param name="start">Filter by the Start Date of the Event</param>
  /// <param name="end">Filter by the End Date of the Event</param>
  /// <param name="level">Filter by the Event Level</param>
  /// <param name="myEvents">Only show events that have at least one registered team associated with the authenticated user.</param>
  /// <param name="eventTypes">Filter by the Event Type</param>
  /// <returns>List of Events</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  public async Task<List<REEvent>> GetEventsAsync(IEnumerable<int> id, IEnumerable<string> sku, IEnumerable<int> team, IEnumerable<int> season, DateTime? start, DateTime? end, IEnumerable<EventLevel> level, bool? myEvents, IEnumerable<EventType> eventTypes)
  {
    return await GetEventsAsync(id, sku, team, season, start, end, level, myEvents, eventTypes, CancellationToken.None).ConfigureAwait(false);
  }
  /// Same as above, plus following 
  /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
  /// <returns>List of Events</returns>
  /// <exception cref="ApiException">A server side error occurred.</exception>
  protected virtual async Task<List<REEvent>> GetEventsAsync(IEnumerable<int> id, IEnumerable<string> sku, IEnumerable<int> team, IEnumerable<int> season, DateTime? start, DateTime? end, IEnumerable<EventLevel> level, bool? myEvents, IEnumerable<EventType> eventTypes, CancellationToken cancellationToken)
  {
    var events = new List<REEvent>();
    var urlBuilder = new StringBuilder();
    urlBuilder.Append(_baseUrl != null ? _baseUrl.TrimEnd('/') : "").Append("/events?");
    if (id != null)
    {
      foreach (var item_ in id) { urlBuilder.Append(Uri.EscapeDataString("id[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (sku != null)
    {
      foreach (var item_ in sku) { urlBuilder.Append(Uri.EscapeDataString("sku[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (team != null)
    {
      foreach (var item_ in team) { urlBuilder.Append(Uri.EscapeDataString("team[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (season != null)
    {
      foreach (var item_ in season) { urlBuilder.Append(Uri.EscapeDataString("season[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (start != null)
    {
      urlBuilder.Append(Uri.EscapeDataString("start") + "=").Append(HttpUtility.UrlEncode(start.Value.ToRobotEventTime())).Append("&");
    }
    if (end != null)
    {
      urlBuilder.Append(Uri.EscapeDataString("end") + "=").Append(HttpUtility.UrlEncode(end.Value.ToRobotEventTime())).Append("&");
    }
    if (level != null)
    {
      foreach (var item_ in level) { urlBuilder.Append(Uri.EscapeDataString("level[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    if (myEvents != null)
    {
      urlBuilder.Append(Uri.EscapeDataString("myEvents") + "=").Append(Uri.EscapeDataString(ConvertToString(myEvents, CultureInfo.InvariantCulture))).Append("&");
    }
    else
    {
      urlBuilder.Append(Uri.EscapeDataString("myEvents") + "=false").Append("&");
    }
    if (eventTypes != null)
    {
      foreach (var item_ in eventTypes) { urlBuilder.Append(Uri.EscapeDataString("eventTypes[]") + "=").Append(Uri.EscapeDataString(ConvertToString(item_, CultureInfo.InvariantCulture))).Append("&"); }
    }
    urlBuilder.Length--;
    urlBuilder.Append(RobotEventConsts.REMaxPerPageTxt);
    
    var eventList = await GetItemsAsync<REEvent>(urlBuilder.ToString(), CancellationToken.None).ConfigureAwait(false);
    return eventList;
  }

}
