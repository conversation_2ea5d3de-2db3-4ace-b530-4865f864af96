﻿using Microsoft.AspNetCore.Identity;
using RoboMentors.Identity.Roles;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Identity;
using Volo.Abp.Users;

namespace RoboMentors.Identity.Users;

public static class UserExtensions
{
  public static void CheckUserFriendlyErrors(this IdentityResult identityResult, string actionBeingPerformed = "perform this action")
  {
    if (identityResult.Succeeded)
    {
      return;
    }

    if (identityResult.Errors == null)
    {
      throw new ArgumentException("identityResult. Errors should not be null.");
    }

    var exception = new AbpIdentityResultException(identityResult);
    throw new UserFriendlyException($"{actionBeingPerformed}. {exception.Message}");
  }

  // Current User Roles
  // -- Event Management
  public static bool IsEventManagement(this ICurrentUser currentUser)
  {
    return currentUser.IsEventAdmin() ||
    currentUser.IsEventPartner() ||
    currentUser.IsDivisionManager();
  }
  public static bool IsEventAdmin(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.EventManagement.Admin.Name);
  }
  public static bool IsEventPartner(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.EventManagement.Partner.Name);
  }
  public static bool IsDivisionManager(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.EventManagement.DivisionManager.Name);
  }
  // -- Judging
  public static bool IsJudging(this ICurrentUser currentUser)
  {
    return currentUser.IsJudgeAdmin() ||
    currentUser.IsJudgeAdvisor() ||
    currentUser.IsJudgeDivisionAdvisor() ||
    currentUser.IsJudge();
  }
  public static bool IsJudgeAdmin(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.Judging.Admin.Name);
  }
  public static bool IsJudgeAdvisor(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.Judging.Advisor.Name);
  }
  public static bool IsJudgeDivisionAdvisor(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.Judging.DivisionAdvisor.Name);
  }
  public static bool IsJudge(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.Judging.Judge.Name);
  }
  public static bool IsDisabledJudge(this ICurrentUser currentUser)
  {
    return currentUser.IsInRole(RoboMentorsRoles.Judging.DisabledJudge.Name);
  }

}
