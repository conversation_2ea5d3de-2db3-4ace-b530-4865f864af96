﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RoboMentors.EventManagement.RobotEvents;

public static class RobotEventConsts
{
  public const string REBaseUrl = "https://www.robotevents.com/api/v2";
  public const string REScheme = "Bearer";
  public const string REKey = "*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
  public const int REStartOffset = -7;
  public const int REEndOffset = 90;
  public const string REMaxPerPageTxt = "&per_page=250";
  public const string REMaxPerPageTxt2 = "?per_page=250";

  public const string REWCBaseUrl = "https://www.robotevents.com/api/";
  public const string REWCKey = "un2yO9oKLkqxxjL3Kmb9JvQenl8RGU0eB0f1xHXY";

}
