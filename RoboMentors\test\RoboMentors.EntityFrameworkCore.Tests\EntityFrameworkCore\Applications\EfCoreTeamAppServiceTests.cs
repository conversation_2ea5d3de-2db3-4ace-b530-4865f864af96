﻿using RoboMentors.EventManagement.Teams;
using Xunit;

namespace RoboMentors.EntityFrameworkCore.Applications;

[Collection(RoboMentorsTestConsts.CollectionDefinitionName)]
public class EfCoreTeamAppServiceTests : TeamAppService_Tests<RoboMentorsEntityFrameworkCoreTestModule>
{
  // This concrete class inherits all the test methods from TeamAppService_Tests
  // and provides the specific TStartupModule (RoboMentorsEntityFrameworkCoreTestModule)
  // which includes Entity Framework Core setup with SQLite in-memory database
}
