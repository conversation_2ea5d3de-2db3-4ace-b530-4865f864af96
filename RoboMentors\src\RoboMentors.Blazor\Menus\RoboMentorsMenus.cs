﻿namespace RoboMentors.Blazor.Menus;

public class RoboMentorsMenus
{
    private const string Prefix = "RoboMentors";
    public const string Home = Prefix + ".Home";

  //Add your menu items here...

  // **************************
  // * Event Management
  // **************************
  public const string EventManagementGroupName = ".EventManagement";
  public static class EventManagement
  {
    public const string Menu = Prefix + EventManagementGroupName;
    public const string Events = Menu + ".Events";
  }

  // **************************
  // * Maintenance
  // **************************
  public const string MaintenanceGroupName = ".Maintenance";
  public static class Maintenance
  {
    public const string Menu = Prefix + MaintenanceGroupName;

    // * Event Maintenance
    public static class EventManagement
    {
      public const string Regions = Menu + ".Regions";
    }

    // * Judging Maintenance
    public static class Judging
    {
      public const string Awards = Menu + ".Awards";
      public const string Questions = Menu + ".Questions";
    }
  }
}
