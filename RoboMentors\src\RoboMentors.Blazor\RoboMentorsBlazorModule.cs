using Blazorise.Bootstrap5;
using Blazorise.Icons.FontAwesome;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using OpenIddict.Server.AspNetCore;
using OpenIddict.Validation.AspNetCore;
using RoboMentors.Blazor.Components;
using RoboMentors.Blazor.Components.Pages.Components;
using RoboMentors.Blazor.HealthChecks;
using RoboMentors.Blazor.Menus;
using RoboMentors.EntityFrameworkCore;
using RoboMentors.Localization;
using RoboMentors.MultiTenancy;
using System;
using System.IO;
using System.Security.Cryptography.X509Certificates;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Components.Server;
using Volo.Abp.AspNetCore.Components.Server.LeptonXLiteTheme;
using Volo.Abp.AspNetCore.Components.Server.LeptonXLiteTheme.Bundling;
using Volo.Abp.AspNetCore.Components.Web;
using Volo.Abp.AspNetCore.Components.Web.Theming.Routing;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement.Blazor.Server;
using Volo.Abp.Identity;
using Volo.Abp.Identity.Blazor.Server;
using Volo.Abp.Localization;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.Security.Claims;
using Volo.Abp.SettingManagement.Blazor.Server;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.Ui.LayoutHooks;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;

namespace RoboMentors.Blazor;

[DependsOn(
    typeof(RoboMentorsApplicationModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(RoboMentorsEntityFrameworkCoreModule),
    typeof(RoboMentorsHttpApiModule),
    typeof(AbpAutofacModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpIdentityBlazorServerModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpAspNetCoreComponentsServerLeptonXLiteThemeModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(AbpFeatureManagementBlazorServerModule),
    typeof(AbpSettingManagementBlazorServerModule)
   )]
public class RoboMentorsBlazorModule : AbpModule
{
  public override void PreConfigureServices(ServiceConfigurationContext context)
  {
    var hostingEnvironment = context.Services.GetHostingEnvironment();
    var configuration = context.Services.GetConfiguration();

    context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
    {
      options.AddAssemblyResource(
          typeof(RoboMentorsResource),
          typeof(RoboMentorsDomainModule).Assembly,
          typeof(RoboMentorsDomainSharedModule).Assembly,
          typeof(RoboMentorsApplicationModule).Assembly,
          typeof(RoboMentorsApplicationContractsModule).Assembly,
          typeof(RoboMentorsBlazorModule).Assembly
      );
    });

    PreConfigure<OpenIddictBuilder>(builder =>
    {
      builder.AddValidation(options =>
      {
          options.AddAudiences("RoboMentors");
          options.UseLocalServer();
          options.UseAspNetCore();
      });
    });

    if (!hostingEnvironment.IsDevelopment())
    {
      PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
      {
        options.AddDevelopmentEncryptionAndSigningCertificate = false;
      });

      PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
      {
        serverBuilder.AddProductionEncryptionAndSigningCertificate("openiddict.pfx", configuration["AuthServer:CertificatePassPhrase"]!);
        serverBuilder.SetIssuer(new Uri(configuration["AuthServer:Authority"]!));
      });
    }

    PreConfigure<AbpAspNetCoreComponentsWebOptions>(options =>
    {
      options.IsBlazorWebApp = true;
    });
  }

  public override void ConfigureServices(ServiceConfigurationContext context)
  {
    var hostingEnvironment = context.Services.GetHostingEnvironment();
    var configuration = context.Services.GetConfiguration();

    // Add services to the container.
    context.Services.AddRazorComponents()
      .AddInteractiveServerComponents();

    if (!configuration.GetValue<bool>("App:DisablePII"))
    {
      Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
      Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
    }

    if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
    {
      Configure<OpenIddictServerAspNetCoreOptions>(options =>
      {
        options.DisableTransportSecurityRequirement = true;
      });
            
      Configure<ForwardedHeadersOptions>(options =>
      {
        options.ForwardedHeaders = ForwardedHeaders.XForwardedProto;
      });
    }

    ConfigureAuthentication(context);
    ConfigureUrls(configuration);
    ConfigureBundles();
    ConfigureHealthChecks(context);
    ConfigureAutoMapper();
    ConfigureVirtualFileSystem(hostingEnvironment);
    ConfigureSwaggerServices(context.Services);
    ConfigureAutoApiControllers();
    ConfigureBlazorise(context);
    ConfigureRouter(context);
    ConfigureMenu(context);
    ConfigureLayoutHooks(context);

  }

  private void ConfigureAuthentication(ServiceConfigurationContext context)
  {
    context.Services.ForwardIdentityAuthenticationForBearer(OpenIddictValidationAspNetCoreDefaults.AuthenticationScheme);
    context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
    {
      options.IsDynamicClaimsEnabled = true;
    });
  }

  private void ConfigureUrls(IConfiguration configuration)
  {
    Configure<AppUrlOptions>(options =>
    {
      options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
      options.RedirectAllowedUrls.AddRange(configuration["App:RedirectAllowedUrls"]?.Split(',') ?? Array.Empty<string>());
    });
  }

  private void ConfigureBundles()
  {
    Configure<AbpBundlingOptions>(options =>
    {
      // MVC UI
      options.StyleBundles.Configure(
        LeptonXLiteThemeBundles.Styles.Global,
        bundle =>
        {
          bundle.AddFiles("/global-styles.css");
        }
      );

      options.ScriptBundles.Configure(
        LeptonXLiteThemeBundles.Scripts.Global,
        bundle =>
        {
          bundle.AddFiles("/global-scripts.js");
        }
      );

      // Blazor UI
      options.StyleBundles.Configure(
        BlazorLeptonXLiteThemeBundles.Styles.Global,
        bundle =>
        {
          bundle.AddFiles("/global-styles.css");
        }
      );
    });
  }

  private void ConfigureHealthChecks(ServiceConfigurationContext context)
  {
    context.Services.AddRoboMentorsHealthChecks();
  }

  private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
  {
    if (hostingEnvironment.IsDevelopment())
    {
      Configure<AbpVirtualFileSystemOptions>(options =>
      {
        options.FileSets.ReplaceEmbeddedByPhysical<RoboMentorsDomainSharedModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}RoboMentors.Domain.Shared"));
        options.FileSets.ReplaceEmbeddedByPhysical<RoboMentorsDomainModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}RoboMentors.Domain"));
        options.FileSets.ReplaceEmbeddedByPhysical<RoboMentorsApplicationContractsModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}RoboMentors.Application.Contracts"));
        options.FileSets.ReplaceEmbeddedByPhysical<RoboMentorsApplicationModule>(Path.Combine(hostingEnvironment.ContentRootPath, $"..{Path.DirectorySeparatorChar}RoboMentors.Application"));
        options.FileSets.ReplaceEmbeddedByPhysical<RoboMentorsBlazorModule>(hostingEnvironment.ContentRootPath);
      });
    }
  }

  private void ConfigureSwaggerServices(IServiceCollection services)
  {
    services.AddAbpSwaggerGen(
      options =>
      {
        options.SwaggerDoc("v1", new OpenApiInfo { Title = "RoboMentors API", Version = "v1" });
        options.DocInclusionPredicate((docName, description) => true);
        options.CustomSchemaIds(type => type.FullName);
      }
    );
  }


  private void ConfigureBlazorise(ServiceConfigurationContext context)
  {
    context.Services
      .AddBootstrap5Providers()
      .AddFontAwesomeIcons();
  }

  private void ConfigureMenu(ServiceConfigurationContext context)
  {
    Configure<AbpNavigationOptions>(options =>
    {
      options.MenuContributors.Add(new RoboMentorsMenuContributor());
    });
  }

  private void ConfigureLayoutHooks(ServiceConfigurationContext context)
  {
    Configure<AbpLayoutHookOptions>(options =>
    {
      options.Add(LayoutHooks.Body.First,
          typeof(EnvironmentVersionComponent)
      );
    });
    Configure<AbpLayoutHookOptions>(options =>
    {
      options.Add(LayoutHooks.Body.Last,
          typeof(FooterComponent)
      );
    });
  }

  private void ConfigureRouter(ServiceConfigurationContext context)
  {
    Configure<AbpRouterOptions>(options =>
    {
      options.AppAssembly = typeof(RoboMentorsBlazorModule).Assembly;
    });
  }

  private void ConfigureAutoApiControllers()
  {
    Configure<AbpAspNetCoreMvcOptions>(options =>
    {
      options.ConventionalControllers.Create(typeof(RoboMentorsApplicationModule).Assembly);
    });
  }

  private void ConfigureAutoMapper()
  {
    Configure<AbpAutoMapperOptions>(options =>
    {
      options.AddMaps<RoboMentorsBlazorModule>();
    });
  }

  public override void OnApplicationInitialization(ApplicationInitializationContext context)
  {
    var env = context.GetEnvironment();
    var app = context.GetApplicationBuilder();

    app.UseForwardedHeaders();

    if (env.IsDevelopment())
    {
      app.UseDeveloperExceptionPage();
    }

    app.UseAbpRequestLocalization();

    if (!env.IsDevelopment())
    {
      app.UseErrorPage();
      app.UseHsts();
    }

    app.UseCorrelationId();
    app.UseRouting();
    app.MapAbpStaticAssets();
    app.UseAbpStudioLink();
    app.UseAbpSecurityHeaders();
    app.UseAntiforgery();
    app.UseAuthentication();
    app.UseAbpOpenIddictValidation();

    if (MultiTenancyConsts.IsEnabled)
    {
        app.UseMultiTenancy();
    }

    app.UseUnitOfWork();
    app.UseDynamicClaims();
    app.UseAuthorization();
    app.UseSwagger();
    app.UseAbpSwaggerUI(options =>
    {
      options.SwaggerEndpoint("/swagger/v1/swagger.json", "RoboMentors API");
    });
    app.UseAuditing();
    app.UseAbpSerilogEnrichers();
    app.UseConfiguredEndpoints(builder =>
    {
      builder.MapRazorComponents<App>()
        .AddInteractiveServerRenderMode()
        .AddAdditionalAssemblies(builder.ServiceProvider.GetRequiredService<IOptions<AbpRouterOptions>>().Value.AdditionalAssemblies.ToArray());
    });
  }
}
